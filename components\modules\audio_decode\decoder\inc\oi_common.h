/******************************************************************************
 *
 *  Copyright (C) 2014 The Android Open Source Project
 *  Copyright 2002 - 2004 Open Interface North America, Inc. All rights reserved.
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at:
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 ******************************************************************************/
#ifndef _OI_COMMON_H
#define _OI_COMMON_H
/**
 * @file
 *
 * This file is used to group commonly used BLUEmagic 3.0 software
 * header files.
 *
 * This file should be included in application source code along with the header
 * files for the specific modules of the protocol stack being used.
 */

/**********************************************************************************
  $Revision: #1 $
***********************************************************************************/

#include "oi_bt_spec.h"
#include "oi_stddefs.h"
#include "oi_status.h"
#include "oi_time.h"
#include "oi_osinterface.h"


/*****************************************************************************/
#endif /* _OI_COMMON_H */
