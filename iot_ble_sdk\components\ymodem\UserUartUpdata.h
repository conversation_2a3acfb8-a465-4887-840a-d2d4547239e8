/*
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2023-06-19 14:01:05
 * @LastEditTime: 2023-10-22 17:12:19
 * @Description: 
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */
//打开Ymodem功能
#define YMODEM_START
#ifdef YMODEM_START

#ifndef _USER_UART_UPDATA_H
#define _USER_UART_UPDATA_H

void ymodem_first_pbuf(unsigned char *pbuf, unsigned int len);
void ymodem_erase_flash(unsigned int size);
void ymodem_write_flash(unsigned int dest, unsigned char *src, unsigned int len);
void ymodem_set_firmware(void);

#endif

#endif // YMODEM_START
