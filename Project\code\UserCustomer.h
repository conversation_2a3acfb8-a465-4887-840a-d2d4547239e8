#ifndef _USER_CUSTOMER_H
#define _USER_CUSTOMER_H

#define USER_CUSTOMER_PARAMETER_ADDRESS		0x7D000

enum{
	WAKEUP_BY_ADV = 0,
	WAKEUP_BY_UART = 1,
};

int User_Customer_Load_config (void);
int User_Customer_Set_SlaveMac(unsigned char *Mac, unsigned char WithSave);
int User_Customer_Get_SlaveMac(unsigned char *Mac);
int User_Customer_Reset_SlaveMac(void);
int User_Customer_Set_RemoteID(unsigned char *RemoteID0, unsigned char *RemoteID1, unsigned char WithSave);
int User_Customer_Get_RemoteID(unsigned char *RemoteID0, unsigned char *RemoteID1);
int User_Customer_Set_StudyRemoteID0(unsigned char *StudyRemoteID0, unsigned char WithSave);
int User_Customer_Get_StudyRemoteID0(unsigned char *StudyRemoteID0);
int User_Customer_Reset_StudyRemoteID0(void);
int User_Customer_Set_StudyRemoteID1(unsigned char *StudyRemoteID1, unsigned char WithSave);
int User_Customer_Get_StudyRemoteID1(unsigned char *StudyRemoteID1);
int User_Customer_Reset_StudyRemoteID1(void);
int User_Customer_Save_Default_Para(void);

int User_CustomerInit(void);
int User_CustomerIrq_handler(void);
int User_Customer_adv_report_handle(unsigned char *p);
int User_Customer_Connection_CB(unsigned char Role);
int User_Customer_DisConnection_CB(unsigned char Role);
int User_Customer_BLE_Data_CB(unsigned char *Data, unsigned short Len, unsigned char Role);
int User_Customer_main_loop(void);

// UART定时器管理
int User_Customer_Uart_Timer_Init(void);
int User_Customer_Uart_Timer_Close(void);

#endif
