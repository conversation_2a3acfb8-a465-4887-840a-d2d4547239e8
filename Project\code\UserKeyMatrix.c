/**
 * @file UserKeyMatrix.c
 * @brief 5x5矩阵按键驱动模块 - 改进版：唤醒后延迟启动扫描
 * <AUTHOR>
 * @date 2024-12-19
 * @version 3.1 - 唤醒键值保护版：BLE连接前禁止扫描
 */

#include "UserKeyMatrix.h"
#include "UserKeyAdv.h"
#include "UserPrintf.h"
#include "driver_gpio.h"
#include "driver_iomux.h"
#include "driver_pmu.h"
#include "driver_system.h"
#include "driver_wdt.h"
#include "os_timer.h"
#include "sys_utils.h"

// 启用调试输出
#define Key_Matrix_Debug(_x)                                                   \
  do {                                                                         \
    co_printf _x;                                                              \
  } while (0)

// 矩阵按键引脚配置
static const matrix_key_pin_config_t g_default_pin_config = {
    .rows = {{GPIO_PORT_A, GPIO_BIT_7, PORTA7_FUNC_A7, 0},
             {GPIO_PORT_A, GPIO_BIT_0, PORTA0_FUNC_A0, 0},
             {GPIO_PORT_A, GPIO_BIT_1, PORTA1_FUNC_A1, 0},
             {GPIO_PORT_A, GPIO_BIT_2, PORTA2_FUNC_A2, 0}},
    .cols = {{GPIO_PORT_A, GPIO_BIT_3, PORTA3_FUNC_A3, GPIO_PA3},
             {GPIO_PORT_C, GPIO_BIT_6, PORTC6_FUNC_C6, GPIO_PC6},
             {GPIO_PORT_C, GPIO_BIT_7, PORTC7_FUNC_C7, GPIO_PC7},
             {GPIO_PORT_D, GPIO_BIT_4, PORTD4_FUNC_D4, GPIO_PD4},
             {GPIO_PORT_D, GPIO_BIT_5, PORTD5_FUNC_D5, GPIO_PD5}}};

// 按键驱动状态结构体
typedef struct {
  key_config_t config;
  matrix_key_pin_config_t pin_config;
  uint8_t initialized;
  uint8_t scan_active;
  uint8_t scan_enabled; // 新增：扫描使能标志，用于控制是否允许扫描
} key_driver_t;

// 全局变量
static key_driver_t g_key_driver = {0};
static os_timer_t g_key_scan_timer;

// 唤醒相关变量
static uint8_t g_wakeup_recovery_needed = 0;

// 唤醒按键缓存 - 严格保护，防止被覆盖
static uint32_t g_wakeup_key_cache = 0;
static uint8_t g_wakeup_key_pending = 0;
static uint8_t g_wakeup_key_protected = 0; // 新增：唤醒键值保护标志

// 外部BLE连接标志位
extern unsigned char User_Ble_Send_EN;

// 按键状态管理
static uint32_t g_last_key_mask = 0;

// 静态函数声明
static void key_scan_timer_handler(void *arg);
static void key_process_and_send(uint32_t key_mask);
static void key_gpio_init_matrix(void);
static uint32_t key_matrix_scan_hardware(void);
static void key_enter_low_power_mode(void);
static void key_gpio_ensure_state(void);

/**
 * @brief 检查并处理缓存的唤醒按键（严格保护版）
 * @return 0-无缓存按键，1-有缓存按键已处理
 */
int user_key_check_wakeup_cache(void) {
  if (g_wakeup_key_pending && g_wakeup_key_protected && User_Ble_Send_EN) {
    Key_Matrix_Debug(("BLE connected! Sending protected wakeup key: 0x%08X\r\n",
                      g_wakeup_key_cache));

    // 立即清除保护标志，防止重复调用
    uint32_t cached_key = g_wakeup_key_cache;
    g_wakeup_key_pending = 0;
    g_wakeup_key_protected = 0;
    g_wakeup_key_cache = 0;

    if (g_key_driver.config.callback) {
      key_info_t wakeup_info = {0};
      wakeup_info.key_mask = cached_key;
      wakeup_info.event = KEY_EVENT_PRESS;
      wakeup_info.duration = 0;
      g_key_driver.config.callback(&wakeup_info);
    }

    g_last_key_mask = cached_key;

    Key_Matrix_Debug(("Wakeup key sent and protection cleared\r\n"));
    return 1;
  }
  return 0;
}

/**
 * @brief 按键驱动初始化（改进版：不立即启动扫描）
 */
int user_key_init(key_config_t *config) {
  if (config == NULL) {
    return -1;
  }

  if (g_key_driver.initialized) {
    return 0;
  }

  g_key_driver.config = *config;
  g_key_driver.pin_config = g_default_pin_config;

  key_gpio_init_matrix();

  // 初始化定时器但不启动
  os_timer_init(&g_key_scan_timer, key_scan_timer_handler, NULL);

  g_key_driver.initialized = 1;
  g_key_driver.scan_active = 0;  // 标记为未启动
  g_key_driver.scan_enabled = 0; // 默认禁用扫描
  g_last_key_mask = 0;

  Key_Matrix_Debug(
      ("Key matrix initialized, scanning disabled until BLE ready\r\n"));
  return 0;
}

/**
 * @brief 启动按键扫描（BLE连接成功后调用）
 */
int user_key_start_scanning(void) {
  if (!g_key_driver.initialized) {
    Key_Matrix_Debug(("Key driver not initialized\r\n"));
    return -1;
  }

  if (g_key_driver.scan_active) {
    Key_Matrix_Debug(("Key scanning already active\r\n"));
    return 0;
  }

  g_key_driver.scan_enabled = 1;
  g_key_driver.scan_active = 1;
  os_timer_start(&g_key_scan_timer, g_key_driver.config.scan_interval, 0);

  Key_Matrix_Debug(("Key scanning started after BLE connection\r\n"));
  return 0;
}

/**
 * @brief 停止按键扫描
 */
int user_key_stop_scanning(void) {
  if (g_key_driver.scan_active) {
    os_timer_stop(&g_key_scan_timer);
    g_key_driver.scan_active = 0;
    g_key_driver.scan_enabled = 0;
    Key_Matrix_Debug(("Key scanning stopped\r\n"));
  }
  return 0;
}

/**
 * @brief 按键扫描（手动调用）- 增加保护检查
 */
int user_key_scan(void) {
  if (!g_key_driver.initialized) {
    return -1;
  }

  // 检查是否允许扫描（保护唤醒键值期间禁止扫描）
  if (!g_key_driver.scan_enabled) {
    Key_Matrix_Debug(("Key scan blocked - waiting for BLE connection\r\n"));
    return 0;
  }

  uint32_t key_mask = key_matrix_scan_hardware();
  key_process_and_send(key_mask);
  return 0;
}

/**
 * @brief 进入低功耗模式
 */
int user_key_enter_low_power(void) {
  if (!g_key_driver.initialized) {
    return -1;
  }

  // 检查：如果当前有按键按下，拒绝进入低功耗模式
  uint32_t current_key_mask = key_matrix_scan_hardware();
  if (current_key_mask != 0) {
    Key_Matrix_Debug(
        ("Refuse to enter low power - key still pressed: 0x%08X\r\n",
         current_key_mask));
    return -2;
  }

  Key_Matrix_Debug(("Entering low power mode\r\n"));
  user_key_stop_scanning(); // 停止扫描
  g_last_key_mask = 0;

  key_enter_low_power_mode();
  return 0;
}

/**
 * @brief 矩阵按键低功耗配置
 */
int user_key_matrix_low_power_config(void) {
  const matrix_key_pin_config_t *pin_cfg = &g_key_driver.pin_config;

  for (int i = 0; i < KEY_MATRIX_ROWS; i++) {
    pmu_set_pin_to_PMU(pin_cfg->rows[i].port, pin_cfg->rows[i].bit);
    pmu_set_pin_pull(pin_cfg->rows[i].port, BIT(pin_cfg->rows[i].bit), true);
  }

  for (int i = 0; i < KEY_MATRIX_COLS; i++) {
    pmu_set_port_mux(pin_cfg->cols[i].port, pin_cfg->cols[i].bit,
                     PMU_PORT_MUX_GPIO);
    pmu_set_pin_to_PMU(pin_cfg->cols[i].port, BIT(pin_cfg->cols[i].bit));
    pmu_set_pin_dir(pin_cfg->cols[i].port, BIT(pin_cfg->cols[i].bit),
                    GPIO_DIR_OUT);
    pmu_set_gpio_value(pin_cfg->cols[i].port, BIT(pin_cfg->cols[i].bit), 0);
  }

  return 0;
}

// ======================== 静态函数实现 ========================

/**
 * @brief 按键扫描定时器处理函数
 */
static void key_scan_timer_handler(void *arg) {
  (void)arg;

  // 检查扫描是否被禁用
  if (!g_key_driver.scan_enabled) {
    Key_Matrix_Debug(("Timer scan blocked - BLE not ready\r\n"));
    return;
  }

  user_key_scan();
  // 启动下一次扫描
  os_timer_start(&g_key_scan_timer, g_key_driver.config.scan_interval, 0);
}

/**
 * @brief 按键处理和发送（保护唤醒键值版）
 * @param key_mask 当前扫描到的按键掩码
 */
static void key_process_and_send(uint32_t key_mask) {
  Key_Matrix_Debug(
      ("Key scan: current=0x%08X, last=0x%08X\r\n", key_mask, g_last_key_mask));

  // 智能保护：只阻塞不同的新按键，允许按键释放和相同按键
  if (g_wakeup_key_protected) {
    // 只阻塞不同的新按键，允许按键释放(key_mask==0)和相同按键
    if (key_mask != 0 && key_mask != g_wakeup_key_cache) {
      Key_Matrix_Debug(("Key processing blocked - wakeup key protected (current=0x%08X, cached=0x%08X)\r\n",
                        key_mask, g_wakeup_key_cache));
      return;
    }
    Key_Matrix_Debug(("Key processing allowed - release or same key (current=0x%08X, cached=0x%08X)\r\n",
                      key_mask, g_wakeup_key_cache));
  }

  // 优先处理缓存的唤醒按键
  if (g_wakeup_key_pending) {
    Key_Matrix_Debug(
        ("Processing cached wakeup key: 0x%08X\r\n", g_wakeup_key_cache));

    if (g_key_driver.config.callback) {
      key_info_t wakeup_info = {0};
      wakeup_info.key_mask = g_wakeup_key_cache;
      wakeup_info.event = KEY_EVENT_PRESS;
      wakeup_info.duration = 0;
      g_key_driver.config.callback(&wakeup_info);
    }

    g_last_key_mask = g_wakeup_key_cache;
    g_wakeup_key_pending = 0;
    g_wakeup_key_cache = 0;
    return;
  }

  // 按键变化检测和持续按键处理
  if (key_mask != g_last_key_mask) {
    Key_Matrix_Debug(("Key change detected: 0x%08X -> 0x%08X\r\n",
                      g_last_key_mask, key_mask));

    // 发送按键事件
    if (g_key_driver.config.callback) {
      key_info_t key_info = {0};
      key_info.key_mask = key_mask;
      key_info.event = KEY_EVENT_PRESS;
      key_info.duration = 0;
      g_key_driver.config.callback(&key_info);
    }

    g_last_key_mask = key_mask;
  } else if (key_mask != 0) {
    // 持续按键状态：即使没有变化，也定期发送状态以保持活跃
    // 这可以防止在长按时BLE连接被断开
    Key_Matrix_Debug(("Key held: 0x%08X (keeping connection alive)\r\n", key_mask));
    
    if (g_key_driver.config.callback) {
      key_info_t key_info = {0};
      key_info.key_mask = key_mask;
      key_info.event = KEY_EVENT_PRESS;
      key_info.duration = 0;
      g_key_driver.config.callback(&key_info);
    }
  }
}

/**
 * @brief 矩阵按键GPIO初始化
 */
static void key_gpio_init_matrix(void) {
  const matrix_key_pin_config_t *pin_cfg = &g_key_driver.pin_config;

  // 行引脚配置为输入上拉
  for (int i = 0; i < KEY_MATRIX_ROWS; i++) {
    pmu_set_pin_to_CPU(pin_cfg->rows[i].port, BIT(pin_cfg->rows[i].bit));
    for (volatile int j = 0; j < 100; j++)
      ;

    system_set_port_mux(pin_cfg->rows[i].port, pin_cfg->rows[i].bit,
                        pin_cfg->rows[i].func);
    gpio_set_dir(pin_cfg->rows[i].port, pin_cfg->rows[i].bit, GPIO_DIR_IN);

    uint32_t gpio_mask = (1 << pin_cfg->rows[i].bit);
    system_set_port_pull(gpio_mask, 1);
  }

  // 列引脚配置为输出高电平
  for (int i = 0; i < KEY_MATRIX_COLS; i++) {
    pmu_set_pin_to_CPU(pin_cfg->cols[i].port, BIT(pin_cfg->cols[i].bit));
    for (volatile int j = 0; j < 100; j++)
      ;

    system_set_port_mux(pin_cfg->cols[i].port, pin_cfg->cols[i].bit,
                        pin_cfg->cols[i].func);
    gpio_set_dir(pin_cfg->cols[i].port, pin_cfg->cols[i].bit, GPIO_DIR_OUT);
    gpio_set_pin_value(pin_cfg->cols[i].port, pin_cfg->cols[i].bit, 1);
  }
}

/**
 * @brief 轻量级GPIO状态确保函数
 */
static void key_gpio_ensure_state(void) {
  const matrix_key_pin_config_t *pin_cfg = &g_key_driver.pin_config;

  // 行引脚确保为输入
  for (int i = 0; i < KEY_MATRIX_ROWS; i++) {
    gpio_set_dir(pin_cfg->rows[i].port, pin_cfg->rows[i].bit, GPIO_DIR_IN);
  }

  // 列引脚确保为输出高电平
  for (int i = 0; i < KEY_MATRIX_COLS; i++) {
    gpio_set_dir(pin_cfg->cols[i].port, pin_cfg->cols[i].bit, GPIO_DIR_OUT);
    gpio_set_pin_value(pin_cfg->cols[i].port, pin_cfg->cols[i].bit, 1);
  }
}

extern void send_key_data_delay(void);
static uint32_t key_matrix_scan_hardware(void) {
  uint32_t key_mask = 0;
  wdt_feed();

  if (g_wakeup_recovery_needed) {
    key_gpio_init_matrix();
    g_wakeup_recovery_needed = 0;
  } else {
    key_gpio_ensure_state();
  }

  const matrix_key_pin_config_t *pin_cfg = &g_key_driver.pin_config;

  // 预先配置所有列为高电平
  for (int i = 0; i < KEY_MATRIX_COLS; i++) {
    gpio_set_pin_value(pin_cfg->cols[i].port, pin_cfg->cols[i].bit, 1);
  }

  // 逐列扫描
  for (int col = 0; col < KEY_MATRIX_COLS; col++) {
    gpio_set_pin_value(pin_cfg->cols[col].port, pin_cfg->cols[col].bit, 0);

    for (volatile int i = 0; i < 10; i++)
      ;

    // 检查所有行
    for (int row = 0; row < KEY_MATRIX_ROWS; row++) {
      int pin_value =
          gpio_get_pin_value(pin_cfg->rows[row].port, pin_cfg->rows[row].bit);

      if (pin_value == 0) {
        int bit_pos = row * KEY_MATRIX_COLS + col;
        key_mask |= (1 << bit_pos);
      }
    }

    gpio_set_pin_value(pin_cfg->cols[col].port, pin_cfg->cols[col].bit, 1);
    for (volatile int i = 0; i < 8; i++)
      ;
  }

  if (key_mask != 0) {
    // 启动BLE连接
    User_Conn_Start_Connect();
  }

  send_key_data_delay();
  return key_mask;
}

/**
 * @brief 进入低功耗模式的GPIO配置
 */
static void key_enter_low_power_mode(void) {
  user_key_matrix_low_power_config();
}

/**
 * @brief 唤醒处理函数（改进版：保护唤醒键值，不启动扫描）
 * @return 0-成功，负数-错误码
 */
int user_key_wakeup_handler(void) {
  Key_Matrix_Debug(("=== WAKEUP HANDLER START ===\r\n"));

  const matrix_key_pin_config_t *pin_cfg = &g_default_pin_config;
  uint32_t wakeup_key_mask = 0;

  // 快速GPIO配置和检测
  // 首先配置所有列为输出高电平
  for (int i = 0; i < KEY_MATRIX_COLS; i++) {
    pmu_set_pin_to_CPU(pin_cfg->cols[i].port, BIT(pin_cfg->cols[i].bit));
    system_set_port_mux(pin_cfg->cols[i].port, pin_cfg->cols[i].bit,
                        pin_cfg->cols[i].func);
    gpio_set_dir(pin_cfg->cols[i].port, pin_cfg->cols[i].bit, GPIO_DIR_OUT);
    gpio_set_pin_value(pin_cfg->cols[i].port, pin_cfg->cols[i].bit, 1);
  }

  // 逐行配置并立即检测
  for (int row = 0; row < KEY_MATRIX_ROWS; row++) {
    pmu_set_pin_to_CPU(pin_cfg->rows[row].port, BIT(pin_cfg->rows[row].bit));
    system_set_port_mux(pin_cfg->rows[row].port, pin_cfg->rows[row].bit,
                        pin_cfg->rows[row].func);
    gpio_set_dir(pin_cfg->rows[row].port, pin_cfg->rows[row].bit, GPIO_DIR_IN);
    uint32_t gpio_mask = (1 << pin_cfg->rows[row].bit);
    system_set_port_pull(gpio_mask, 1);

    // 立即检测这一行
    for (int col = 0; col < KEY_MATRIX_COLS; col++) {
      gpio_set_pin_value(pin_cfg->cols[col].port, pin_cfg->cols[col].bit, 0);

      int pin_value =
          gpio_get_pin_value(pin_cfg->rows[row].port, pin_cfg->rows[row].bit);
      if (pin_value == 0) {
        int bit_pos = row * KEY_MATRIX_COLS + col;
        wakeup_key_mask |= (1 << bit_pos);
      }

      gpio_set_pin_value(pin_cfg->cols[col].port, pin_cfg->cols[col].bit, 1);
    }
  }

  // 验证扫描
  if (wakeup_key_mask != 0) {
    Key_Matrix_Debug(("Validation scan for: 0x%08X\r\n", wakeup_key_mask));
    uint32_t validation_mask = 0;
    for (int col = 0; col < KEY_MATRIX_COLS; col++) {
      gpio_set_pin_value(pin_cfg->cols[col].port, pin_cfg->cols[col].bit, 0);
      for (int row = 0; row < KEY_MATRIX_ROWS; row++) {
        int pin_value =
            gpio_get_pin_value(pin_cfg->rows[row].port, pin_cfg->rows[row].bit);
        if (pin_value == 0) {
          int bit_pos = row * KEY_MATRIX_COLS + col;
          validation_mask |= (1 << bit_pos);
        }
      }
      gpio_set_pin_value(pin_cfg->cols[col].port, pin_cfg->cols[col].bit, 1);
    }
    if (validation_mask != 0) {
      wakeup_key_mask = validation_mask;
    }
  }

  // 处理检测结果
  if (wakeup_key_mask != 0) {
    Key_Matrix_Debug(
        ("Wakeup key detected: 0x%08X - PROTECTED\r\n", wakeup_key_mask));

    // 严格保护唤醒键值
    g_wakeup_key_cache = wakeup_key_mask;
    g_wakeup_key_pending = 1;
    g_wakeup_key_protected = 1; // 设置保护标志

    // 启动BLE连接
    User_Conn_Start_Connect();

    Key_Matrix_Debug(("Wakeup key cached and PROTECTED until BLE ready\r\n"));
  } else {
    Key_Matrix_Debug(("No wakeup key detected\r\n"));
  }

  // 设置恢复标志
  g_wakeup_recovery_needed = 1;

  // 重要：不启动扫描定时器，等待BLE连接成功后再启动
  Key_Matrix_Debug(
      ("Wakeup handler completed - scanning DISABLED until BLE ready\r\n"));
  return 0;
}

/**
 * @brief 检查是否有唤醒按键等待发送
 * @return 0-无等待按键，1-有按键等待发送
 */
int user_key_has_pending_wakeup(void) {
  return (g_wakeup_key_pending && g_wakeup_key_protected) ? 1 : 0;
}

/**
 * @brief 获取BLE连接状态
 * @return 0-未连接，1-已连接可发送
 */
int user_key_is_ble_connecting(void) { return User_Ble_Send_EN; }

/**
 * @brief 获取当前硬件按键状态（实时扫描）
 * @return 当前按键掩码，0表示无按键按下
 */
uint32_t user_key_get_current_hardware_state(void) {
  if (!g_key_driver.initialized) {
    return 0;
  }
  
  // 调用硬件扫描函数，但不触发回调
  return key_matrix_scan_hardware();
}
