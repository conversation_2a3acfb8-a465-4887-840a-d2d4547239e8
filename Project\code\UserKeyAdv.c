#include <stdint.h>
#include <string.h>

#include "gap_api.h"
#include "gatt_api.h"

#include "button.h"
#include "jump_table.h"
#include "os_mem.h"
#include "os_timer.h"
#include "sys_utils.h"

#include "UserApp.h"
#include "UserBLE.h"
#include "UserCustomer.h"
#include "UserKeyAdv.h"
#include "UserKeyMatrix.h"
#include "UserPrintf.h"
#include "driver_efuse.h"
#include "driver_flash.h"
#include "driver_gpio.h"
#include "driver_i2s.h"
#include "driver_iomux.h"
#include "driver_plf.h"
#include "driver_pmu.h"
#include "driver_rtc.h"
#include "driver_system.h"
#include "driver_uart.h"
#include "driver_wdt.h"
#include "flash_usage_config.h"

// 调试输出
#define Display_Debug(_x)                                                      \
  do {                                                                         \
    co_printf _x;                                                              \
  } while (0)

/*********************************************** LED & System Control
 * *******************************************/
#define BACKLED_PORT GPIO_PORT_D
#define LED1_PORT GPIO_PORT_D
#define LED2_PORT GPIO_PORT_C
#define LED3_PORT GPIO_PORT_A

#define BACKLED_BIT GPIO_BIT_6
#define LED1_BIT GPIO_BIT_7
#define LED2_BIT GPIO_BIT_5
#define LED3_BIT GPIO_BIT_4

#define BACKLED_ON() gpio_set_pin_value(BACKLED_PORT, BACKLED_BIT, 1)
#define BACKLED_OFF() gpio_set_pin_value(BACKLED_PORT, BACKLED_BIT, 0)

#define LED1_ON() gpio_set_pin_value(LED1_PORT, LED1_BIT, 0)
#define LED1_OFF() gpio_set_pin_value(LED1_PORT, LED1_BIT, 1)

#define LED2_ON() gpio_set_pin_value(LED2_PORT, LED2_BIT, 0)
#define LED2_OFF() gpio_set_pin_value(LED2_PORT, LED2_BIT, 1)

#define LED3_ON() gpio_set_pin_value(LED3_PORT, LED3_BIT, 0)
#define LED3_OFF() gpio_set_pin_value(LED3_PORT, LED3_BIT, 1)

// 主控响应数据结构体
struct master_response_t {
  uint8_t head;
  uint8_t cmd;
  uint8_t sum;
  uint8_t tail;
};

// 按键数据结构体
struct key_t {
  unsigned char key_sta; // 按键状态
  unsigned int key_num;  // 按键值
};

// 系统管理相关变量
unsigned char AdvUserSleepNum;

// 定时器对象
os_timer_t Process_Sleep_Timer;

// 简化的按键状态管理
static uint32_t g_current_key_mask = 0;

extern unsigned char User_Ble_Send_EN;

/**
 * @brief 发送简化的12字节按键数据帧
 * @param keySta 按键状态结构体
 * @param mac MAC地址指针
 */
uint8_t key_test_buf[12] = {0};
uint8_t key_test_buf_len = 0;

static void send_key_data(struct key_t keySta, const uint8_t *mac) {
  // 简化的12字节帧格式
  key_test_buf[0] = 0xAA;           // 帧头
  memcpy(key_test_buf + 1, mac, 6); // MAC地址

  // 按键值 (4字节，大端序)
  uint32_t key_num = keySta.key_num;
  key_test_buf[7] = (key_num >> 24) & 0xFF;
  key_test_buf[8] = (key_num >> 16) & 0xFF;
  key_test_buf[9] = (key_num >> 8) & 0xFF;
  key_test_buf[10] = key_num & 0xFF;

  // 计算校验和
  uint8_t checksum = 0;
  for (uint8_t i = 0; i < 11; i++) {
    checksum += key_test_buf[i];
  }

  key_test_buf[11] = checksum;
  key_test_buf_len = 12;

  Display_Debug(
      ("Key data prepared: mask=0x%08X, chksum=0x%02X\r\n", key_num, checksum));

  // 立即尝试发送
  send_key_data_delay();
}

/**
 * @brief 简化的发送按键数据
 */
void send_key_data_delay(void) {
  if (key_test_buf_len == 12) {
    uint32_t current_key_mask = (key_test_buf[7] << 24) |
                                (key_test_buf[8] << 16) |
                                (key_test_buf[9] << 8) | key_test_buf[10];

    if (User_Ble_Send_EN) {
      // BLE就绪，立即发送
      Display_Debug(
          ("Sending key data via BLE: mask=0x%08X\r\n", current_key_mask));
      user_spple_writenors_data(User_Get_Conn_Libconidx(1), key_test_buf, 12,
                                User_Get_Conn_AttIndex(1));
      key_test_buf_len = 0;
      Display_Debug(("Key data sent successfully\r\n"));
    } else {
      // BLE未就绪，暂存数据等待连接
      Display_Debug(("BLE not ready, data prepared for sending\r\n"));
    }
  }
}

/**
 * @brief 检查是否有按键按下
 */
static unsigned char key_state_has_key_pressed(void) {
  return (g_current_key_mask != 0) ? 1 : 0;
}

/**
 * @brief BLE连接就绪时处理（改进版：先发送唤醒键值，再启动扫描）
 */
void User_BLE_Connection_Ready(void) {
  Display_Debug(("=== BLE Connection Ready ===\r\n"));

  // 第一步：立即处理缓存的唤醒按键
  if (user_key_check_wakeup_cache()) {
    Display_Debug(("Wakeup key sent to BLE\r\n"));
  } else {
    Display_Debug(("No wakeup key to send\r\n"));
  }

  // 第二步：如果有准备好的数据，立即发送
  if (key_test_buf_len == 12) {
    send_key_data_delay();
  }

  // 第三步：启动按键扫描（关键修改）
  Display_Debug(("Starting key scanning after BLE ready\r\n"));
  int scan_result = user_key_start_scanning();
  if (scan_result == 0) {
    Display_Debug(("Key scanning started successfully\r\n"));
  } else {
    Display_Debug(("Failed to start key scanning: %d\r\n", scan_result));
  }
}

/**
 * @brief 简化的矩阵按键事件回调函数
 * @param key_info 按键信息
 */
void matrix_key_event_callback(key_info_t *key_info) {
  Display_Debug(("=== Key Callback: 0x%08X ===\r\n", key_info->key_mask));

  mac_addr_t mac_addr;
  gap_address_get(&mac_addr);

  // 准备按键数据
  struct key_t key_data = {.key_num = key_info->key_mask,
                           .key_sta = (key_info->key_mask != 0) ? 1 : 0};

  // 发送按键数据
  send_key_data(key_data, mac_addr.addr);

  // 更新当前状态
  g_current_key_mask = key_info->key_mask;

  // 任何按键活动都重置睡眠计数（包括按键释放）
  AdvUserSleepNum = 0;
  
  Display_Debug(("Key activity detected, sleep counter reset\r\n"));
}

/**
 * @brief 睡眠处理定时器
 */
__attribute__((section("ram_code"))) uint8_t app_get_ota_state(void);
static void User_Adv_Sleep_Process_Timer(void *arg) {
  AdvUserSleepNum++;
  wdt_feed();

  if (app_get_ota_state()) {
    wdt_stop();
    return;
  }

  // 主动检查是否有唤醒按键需要发送
  if (User_Ble_Send_EN && user_key_has_pending_wakeup()) {
    Display_Debug(("BLE ready during countdown! Sending wakeup key...\r\n"));
    if (user_key_check_wakeup_cache()) {
      Display_Debug(("Wakeup key sent successfully during countdown\r\n"));
      // 启动按键扫描
      user_key_start_scanning();
      // 重置睡眠计数，给更多时间处理按键
      AdvUserSleepNum = 0;
      return;
    }
  }

  // 实时检查硬件按键状态，而不是依赖缓存状态
  uint32_t current_hardware_key_mask = user_key_get_current_hardware_state();

  // 如果检测到任何按键活动，重置睡眠计数
  if (current_hardware_key_mask != 0 || g_current_key_mask != 0) {
    Display_Debug(("Keys active (hw=0x%08X, cache=0x%08X), resetting sleep countdown\r\n",
                   current_hardware_key_mask, g_current_key_mask));
    AdvUserSleepNum = 0;
    return;
  }

  Display_Debug(("Sleep countdown: %d/20\r\n", AdvUserSleepNum));

  if (AdvUserSleepNum == 20) {
    // 最后一次检查：实时扫描硬件按键状态
    uint32_t final_check_mask = user_key_get_current_hardware_state();
    if (final_check_mask != 0) {
      Display_Debug(("Sleep blocked - keys detected in final check (mask=0x%08X)\r\n",
                     final_check_mask));
      AdvUserSleepNum = 8; // 重置为8，给更多时间
      return;
    }
    
    // 检查缓存状态
    if (key_state_has_key_pressed()) {
      Display_Debug(("Sleep blocked - keys still pressed (mask=0x%08X)\r\n",
                     g_current_key_mask));
      AdvUserSleepNum = 9; // 重置为9，下次循环再检查
      return;
    }
    User_DisConnect_BLE(1);
  }

  if (AdvUserSleepNum > 10) {
    AdvUserSleepNum = 0;

    // 进入低功耗模式
    Display_Debug(("Attempting to enter low power mode\r\n"));
    int low_power_result = user_key_enter_low_power();
    if (low_power_result == -2) {
      Display_Debug(("Low power blocked - keys still pressed\r\n"));
      AdvUserSleepNum = 8;
      return;
    } else if (low_power_result != 0) {
      Display_Debug(
          ("Matrix key enter low power failed: %d\r\n", low_power_result));
    }

    // 设置RTC闹钟值
    ool_write32(PMU_REG_RTC_ALMA_VALUE_0, 0x4C);

    // 停止睡眠管理定时器
    os_timer_stop(&Process_Sleep_Timer);

    // 停止看门狗
    wdt_stop();

    // 配置唤醒源
    pmu_port_wakeup_func_set(GPIO_PA7 | GPIO_PA0 | GPIO_PA1 | GPIO_PA2);

    BACKLED_OFF();
    LED1_OFF();
    LED2_OFF();
    LED3_OFF();

    Display_Debug(("Entering deep sleep mode\r\n"));
    co_printf("sleep\r\n");

    // 进入深度睡眠
    system_sleep_enable();
    system_power_off(false);
  }
}

/**
 * @brief 按键处理初始化（简化版）
 */
int User_Adv_Key_Process_Init(void) {
  Display_Debug(("=== Key Process Init (Simplified) ===\r\n"));

  // 初始化LED控制
  system_set_port_mux(BACKLED_PORT, BACKLED_BIT, PORTD6_FUNC_D6);
  gpio_set_dir(BACKLED_PORT, BACKLED_BIT, GPIO_DIR_OUT);

  system_set_port_mux(LED1_PORT, LED1_BIT, PORTD7_FUNC_D7);
  gpio_set_dir(LED1_PORT, LED1_BIT, GPIO_DIR_OUT);

  system_set_port_mux(LED2_PORT, LED2_BIT, PORTC5_FUNC_C5);
  gpio_set_dir(LED2_PORT, LED2_BIT, GPIO_DIR_OUT);

  system_set_port_mux(LED3_PORT, LED3_BIT, PORTA4_FUNC_A4);
  gpio_set_dir(LED3_PORT, LED3_BIT, GPIO_DIR_OUT);

  BACKLED_ON();
  LED1_OFF();
  LED2_OFF();
  LED3_OFF();

  // 初始化矩阵按键模块
  key_config_t key_config = {.scan_interval = 3, // 3ms扫描间隔
                             .callback =
                                 (key_callback_t)matrix_key_event_callback};

  Display_Debug(("Initializing matrix key module...\r\n"));
  int key_init_result = user_key_init(&key_config);
  if (key_init_result == 0) {
    Display_Debug(("Matrix key module initialized successfully\r\n"));
  } else {
    Display_Debug(
        ("Matrix key initialization failed: %d\r\n", key_init_result));
  }

  // 启动睡眠管理定时器
  os_timer_init(&Process_Sleep_Timer, User_Adv_Sleep_Process_Timer, NULL);
  os_timer_start(&Process_Sleep_Timer, 300, 1);

  // 初始化系统变量
  AdvUserSleepNum = 0;
  g_current_key_mask = 0;

  Display_Debug(("=== Key Process Init Complete ===\r\n"));
  return 0;
}

/************************************************* Scan
 * *********************************************/
int User_Rcv_Remote_Data_Process(unsigned char *data, unsigned char len) {
  // 数据接收调试信息
  Display_Debug(("=== Remote Data Process Start ===\r\n"));
  Display_Debug(("Received data length: %d\r\n", len));

  // 打印接收到的原始数据
  Display_Debug(("Raw data: "));
  for (int i = 0; i < len && i < 16; i++) {
    Display_Debug(("0x%02X ", data[i]));
  }
  Display_Debug(("\r\n"));

  // 检查数据长度
  if (len < sizeof(struct master_response_t)) {
    Display_Debug(("Data length too short, expected: %d, received: %d\r\n",
                   sizeof(struct master_response_t), len));
    return 0;
  }

  struct master_response_t *response = (struct master_response_t *)data;

  Display_Debug(("Frame structure - Head: 0x%02X, Cmd: 0x%02X, Sum: 0x%02X, "
                 "Tail: 0x%02X\r\n",
                 response->head, response->cmd, response->sum, response->tail));

  // 验证帧头
  if (response->head != 0x55) {
    Display_Debug(("Frame head error, expected: 0x55, received: 0x%02X\r\n",
                   response->head));
    return 0;
  }
  Display_Debug(("Frame head OK\r\n"));

  // 验证帧尾
  if (response->tail != 0xfe) {
    Display_Debug(("Frame tail error, expected: 0xFE, received: 0x%02X\r\n",
                   response->tail));
    return 0;
  }
  Display_Debug(("Frame tail OK\r\n"));

  // 验证校验和
  uint8_t sum = response->head + response->cmd;
  if (sum != response->sum) {
    Display_Debug(("Checksum error, calculated: 0x%02X, received: 0x%02X\r\n",
                   sum, response->sum));
    return 0;
  }
  Display_Debug(("Checksum OK\r\n"));

  // 处理命令
  Display_Debug(("Processing command: 0x%02X\r\n", response->cmd));

  if (response->cmd == 0x01) {
    Display_Debug(("CMD 0x01: Save pairing MAC address\r\n"));
    User_Customer_Save_Default_Para(); // 存储配对MAC
    
    // 闪烁BACKLED 3次指示配对成功
    for (int i = 0; i < 3; i++) {
      BACKLED_ON();
      for (int j = 0; j < 5000; j++) {
        wdt_feed();
        if (j % 1000 == 0) wdt_feed(); // 额外看门狗喂狗
      }
      BACKLED_OFF();
      for (int j = 0; j < 5000; j++) {
        wdt_feed();
        if (j % 1000 == 0) wdt_feed(); // 额外看门狗喂狗
      }
      wdt_feed(); // 每次循环结束额外喂狗
    }
    
    Display_Debug(("Pairing MAC saved successfully with LED indication\r\n"));
  } else if (response->cmd == 0x02) {
    Display_Debug(("CMD 0x02: Reset slave MAC address\r\n"));
    User_Customer_Reset_SlaveMac(); // 清除配对MAC
    
    // 闪烁BACKLED 3次指示MAC地址已清除
    for (int i = 0; i < 3; i++) {
      BACKLED_ON();
      for (int j = 0; j < 5000; j++) {
        wdt_feed();
        if (j % 1000 == 0) wdt_feed(); // 额外看门狗喂狗
      }
      BACKLED_OFF();
      for (int j = 0; j < 5000; j++) {
        wdt_feed();
        if (j % 1000 == 0) wdt_feed(); // 额外看门狗喂狗
      }
      wdt_feed(); // 每次循环结束额外喂狗
    }
    
    Display_Debug(("Slave MAC reset successfully with LED indication\r\n"));
  } else if (response->cmd == 0x03) {
    Display_Debug(("CMD 0x03: Turn on backlight LED\r\n"));
    BACKLED_ON();
    Display_Debug(("Backlight LED turned on\r\n"));
  } else if (response->cmd == 0x04) {
    Display_Debug(("CMD 0x04: LED pattern 1 (LED1 ON, LED2/3 OFF)\r\n"));
    LED1_ON();
    LED2_OFF();
    LED3_OFF();
    Display_Debug(("LED pattern 1 applied\r\n"));
  } else if (response->cmd == 0x05) {
    Display_Debug(("CMD 0x05: LED pattern 2 (LED1/2 ON, LED3 OFF)\r\n"));
    LED1_ON();
    LED2_ON();
    LED3_OFF();
    Display_Debug(("LED pattern 2 applied\r\n"));
  } else if (response->cmd == 0x06) {
    Display_Debug(("CMD 0x06: LED pattern 3 (All LEDs ON)\r\n"));
    LED1_ON();
    LED2_ON();
    LED3_ON();
    Display_Debug(("LED pattern 3 applied\r\n"));
  } else if (response->cmd == 0x07) {
    Display_Debug(("CMD 0x07: Turn off all LEDs\r\n"));
    LED1_OFF();
    LED2_OFF();
    LED3_OFF();
    Display_Debug(("All LEDs turned off\r\n"));
  } else {
    Display_Debug(("Unknown command: 0x%02X\r\n", response->cmd));
  }

  Display_Debug(("=== Remote Data Process End ===\r\n"));
  return 0;
}
