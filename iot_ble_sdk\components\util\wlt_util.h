/**
 * Copyright (c) 2021, wi-linktech
 * 
 * All rights reserved.
 * 
 * 
 */

#ifndef WLT_UTIL_H
#define WLT_UTIL_H

#if defined __cplusplus
extern "C"
{
#endif

#include <stdint.h>
#include <string.h>

//#include "bluetooth.h"
//#include "btstack_defines.h"
//#include "btstack_linked_list.h"
/**
 * @brief Length of a bluetooth device address.
 */
// #define BD_ADDR_LEN 6

   /**
 * @brief Bluetooth address
 */
  //  typedef uint8_t bd_addr_t[BD_ADDR_LEN];

   /* Miscellaneous defined type declarations.                          */

   /* Definitions for compilers that required structure to be explicitly*/
   /* declared as packed.                                               */
#if (defined(__CC_ARM) || defined(__GNUC__))

/* Realview and GCC compilers.                                       */
#define __PACKED_STRUCT_BEGIN__
#define __PACKED_STRUCT_END__ __attribute__((packed))

#elif (defined(__ICCARM__))

/* IAR compiler.                                                     */
#define __PACKED_STRUCT_BEGIN__ __packed
#define __PACKED_STRUCT_END__

#endif

#define ToInt(_x) (((_x) > 0x39) ? (((_x) & ~0x20) - 0x37) : ((_x)-0x30))

   /* The following type declaration represents the structure of a      */
   /* single Bluetooth Board Address.                                   */
   typedef __PACKED_STRUCT_BEGIN__ struct _tagbd_addr_t
   {
      uint8_t bd_addr0;
      uint8_t bd_addr1;
      uint8_t bd_addr2;
      uint8_t bd_addr3;
      uint8_t bd_addr4;
      uint8_t bd_addr5;
   } __PACKED_STRUCT_END__ bd_addr_t;

#define BD_ADDR_SIZE (sizeof(bd_addr_t))

   /* The following MACRO is a utility MACRO that exists to assign      */
   /* the individual Byte values into the specified BD_ADDR variable.   */
   /* The Bytes are NOT in Little Endian Format, however, they are      */
   /* assigned to the BD_ADDR variable in Little Endian Format.  The    */
   /* first parameter is the BD_ADDR variable (of type BD_ADDR_t) to    */
   /* assign, and the next six parameters are the Individual BD_ADDR    */
   /* Byte values to assign to the variable.                            */
#define ASSIGN_BD_ADDR(_dest, _a, _b, _c, _d, _e, _f) \
   {                                                  \
      (_dest).bd_addr0 = (_f);                        \
      (_dest).bd_addr1 = (_e);                        \
      (_dest).bd_addr2 = (_d);                        \
      (_dest).bd_addr3 = (_c);                        \
      (_dest).bd_addr4 = (_b);                        \
      (_dest).bd_addr5 = (_a);                        \
   }

   /* The following MACRO is a utility MACRO that exists to aid in the  */
   /* Comparison of two BD_ADDR_t variables.  This MACRO only returns   */
   /* whether the two BD_ADDR_t variables are equal (MACRO returns      */
   /* boolean result) NOT less than/greater than.  The two parameters to*/
   /* this MACRO are both of type BD_ADDR_t and represent the BD_ADDR_t */
   /* variables to compare.                                             */
#define COMPARE_BD_ADDR(_x, _y) (((_x).bd_addr0 == (_y).bd_addr0) && ((_x).bd_addr1 == (_y).bd_addr1) && ((_x).bd_addr2 == (_y).bd_addr2) && ((_x).bd_addr3 == (_y).bd_addr3) && ((_x).bd_addr4 == (_y).bd_addr4) && ((_x).bd_addr5 == (_y).bd_addr5))

   /* The following MACRO is a utility MACRO that exists to aid in the  */
   /* Comparison of a BD_ADDR_t variables to the NULL BD_ADDR. This     */
   /* MACRO only returns whether the the BD_ADDR_t variable is equal to */
   /* the NULL BD_ADDR (MACRO returns boolean result) NOT less          */
   /* than/greater than.  The parameter to this MACRO is the BD_ADDR_t  */
   /* structure to compare to the NULL BD_ADDR.                         */
#define COMPARE_NULL_BD_ADDR(_x) (((_x).bd_addr0 == 0x00) && ((_x).bd_addr1 == 0x00) && ((_x).bd_addr2 == 0x00) && ((_x).bd_addr3 == 0x00) && ((_x).bd_addr4 == 0x00) && ((_x).bd_addr5 == 0x00))



   typedef char mac_str_t[15];

void bd_addr_to_str(bd_addr_t Board_Address, mac_str_t BoardStr);
void str_to_bd_addr(char *BoardStr, bd_addr_t *Board_Address);
//char * bd_address_to_str(const bd_address_t addr);
unsigned char * bd_address_to_str(bd_addr_t addr);

  //  void BD_ADDR_Change_A(BD_ADDR_t *BD_ADDR, bd_addr_t bd_addr);
  //  void BD_ADDR_Change_B(bd_addr_t bd_addr, BD_ADDR_t *BD_ADDR);

// hack: compilation with the android ndk causes an error as there's a reverse_64 macro
#ifdef reverse_64
#undef reverse_64
#endif

// will be moved to daemon/btstack_device_name_db.h

/**
 * @brief The device name type
 */
#define DEVICE_NAME_LEN 248
   typedef uint8_t device_name_t[DEVICE_NAME_LEN + 1];

   /* API_START */

   /**
 * @brief Minimum function for uint32_t
 * @param a
 * @param b
 * @return value
 */
   uint32_t wlt_min(uint32_t a, uint32_t b);

   /**
 * @brief Maximum function for uint32_t
 * @param a
 * @param b
 * @return value
 */
   uint32_t wlt_max(uint32_t a, uint32_t b);

   /**
 * @brief Calculate delta between two points in time
 * @returns time_a - time_b - result > 0 if time_a is newer than time_b
 */
   int32_t wlt_time_delta(uint32_t time_a, uint32_t time_b);

   /** 
 * @brief Read 16/24/32 bit little endian value from buffer
 * @param buffer
 * @param position in buffer
 * @return value
 */
   uint16_t wlt_little_endian_read_16(const uint8_t *buffer, int position);
   uint32_t wlt_little_endian_read_24(const uint8_t *buffer, int position);
   uint32_t wlt_little_endian_read_32(const uint8_t *buffer, int position);

   /** 
 * @brief Write 16/32 bit little endian value into buffer
 * @param buffer
 * @param position in buffer
 * @param value
 */
   void wlt_little_endian_store_16(uint8_t *buffer, uint16_t position, uint16_t value);
   void wlt_little_endian_store_24(uint8_t *buffer, uint16_t position, uint32_t value);
   void wlt_little_endian_store_32(uint8_t *buffer, uint16_t position, uint32_t value);

   /** 
 * @brief Read 16/24/32 bit big endian value from buffer
 * @param buffer
 * @param position in buffer
 * @return value
 */
   uint32_t wlt_big_endian_read_16(const uint8_t *buffer, int pos);
   uint32_t wlt_big_endian_read_24(const uint8_t *buffer, int pos);
   uint32_t wlt_big_endian_read_32(const uint8_t *buffer, int pos);

   /** 
 * @brief Write 16/32 bit big endian value into buffer
 * @param buffer
 * @param position in buffer
 * @param value
 */
   void wlt_big_endian_store_16(uint8_t *buffer, uint16_t pos, uint16_t value);
   void wlt_big_endian_store_24(uint8_t *buffer, uint16_t pos, uint32_t value);
   void wlt_big_endian_store_32(uint8_t *buffer, uint16_t pos, uint32_t value);

   /**
 * @brief Swap bytes in 16 bit integer
 */
   static inline uint16_t wlt_flip_16(uint16_t value)
   {
      return (uint16_t)((value & 0xff) << 8) | (value >> 8);
   }

   /** 
 * @brief Check for big endian system
 * @returns 1 if on big endian
 */
   static inline int wlt_is_big_endian(void)
   {
      uint16_t sample = 0x0100;
      return *(uint8_t *)&sample;
   }

   /** 
 * @brief Check for little endian system
 * @returns 1 if on little endian
 */
   static inline int wlt_is_little_endian(void)
   {
      uint16_t sample = 0x0001;
      return *(uint8_t *)&sample;
   }

   /**
 * @brief Copy from source to destination and reverse byte order
 * @param src
 * @param dest
 * @param len
 */
   void wlt_reverse_bytes(const uint8_t *src, uint8_t *dest, int len);

   /**
 * @brief Wrapper around reverse_bytes for common buffer sizes
 * @param src
 * @param dest
 */
   void wlt_reverse_24(const uint8_t *src, uint8_t *dest);
   void wlt_reverse_48(const uint8_t *src, uint8_t *dest);
   void wlt_reverse_56(const uint8_t *src, uint8_t *dest);
   void wlt_reverse_64(const uint8_t *src, uint8_t *dest);
   void wlt_reverse_128(const uint8_t *src, uint8_t *dest);
   void wlt_reverse_256(const uint8_t *src, uint8_t *dest);

   /** 
 * @brief ASCII character for 4-bit nibble
 * @return character
 */
   char wlt_char_for_nibble(int nibble);

   /**
 * @brif 4-bit nibble from ASCII character
 * @return value
 */
   int wlt_nibble_for_char(char c);

   /**
 * @brief Parse unsigned number 
 * @param str to parse
 * @return value
 */
   uint32_t wlt_atoi(const char *str);

   /**
 * @brief Return number of digits of a uint32 number
 * @param uint32_number
 * @return num_digits
 */
   int wlt_string_len_for_uint32(uint32_t i);

   /**
 * @brief Return number of set bits in a uint32 number
 * @param uint32_number
 * @return num_set_bits
 */
   int wlt_count_set_bits_uint32(uint32_t x);

   /**
 * CRC8 functions using ETSI TS 101 369 V6.3.0.
 * Only used by RFCOMM
 */
   uint8_t wlt_crc8_check(uint8_t *data, uint16_t len, uint8_t check_sum);
   uint8_t wlt_crc8_calc(uint8_t *data, uint16_t len);

   /* API_END */

#if defined __cplusplus
}
#endif

#endif // BTSTACK_UTIL_H
