/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:48
 * @LastEditTime: 2023-09-05 17:14:11
 * @Description: This file is used to define user application functions.
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */

#ifndef _USER_APP_H
#define _USER_APP_H

#define BLE_NAME_LEN                27//大于28个字节 APP搜不到BLE设备
#define BLE_NAME_BUFFER_LEN         31

#define ADV_INTERVEL_MIN            (0x20)
#define ADV_INTERVEL_MAX            (0x4000)

#define CONN_INTERVEL_MIN           (6)
#define CONN_INTERVEL_MAX           (1600)

typedef enum
{
   BT_RUNMODE_INIT,
   BT_RUNMODE_AT_MODE,
   BT_RUNMODE_TRANSPARENT_MODE,
   BT_RUNMODE_ONE_SLOT_SEND_MODE,
   BT_RUNMODE_WAITING_TO_AT_MODE,
   BT_RUNMODE_MAX,
} BT_RunMode_t;

int user_init(void);
int user_adv_report_event_handle(gap_evt_adv_report_t *adv_rpt);
unsigned short user_crc16(unsigned char *p_msg, unsigned short data_len);
int User_Load_Default_config (void);
int User_Get_Default_Name(unsigned char *Name, unsigned char *length);
int User_Set_Default_Name(unsigned char *Name, unsigned char length, unsigned char WithSave);
int User_Get_Default_Baud (unsigned long *Baud);
int User_Set_Default_Baud(unsigned long Baud, unsigned char WithSave);
int User_Set_Default_Power(unsigned long Power, unsigned char WithSave);
int User_Get_Default_Power (unsigned long *Power);
int User_Get_Default_AdvInterval(unsigned long *AdvIntervalMin, unsigned long *AdvIntervalMax);
int User_Set_Default_AdvInterval(unsigned long AdvIntervalMin, unsigned long AdvIntervalMax, unsigned char WithSave);
int User_Get_Default_ConnParam(unsigned long *ConnParaUpdateUsed, unsigned long *ConnMinInterval, unsigned long *ConnMaxInterval, unsigned long *ConnLatency, unsigned long *ConnTimeout);
int User_Set_Default_ConnParam(unsigned long ConnParaUpdateUsed, unsigned long ConnMinInterval, unsigned long ConnMaxInterval, unsigned long ConnLatency, unsigned long ConnTimeout, unsigned char WithSave);
int User_Get_Default_SmpConfig(unsigned long *SmpMode, unsigned long *PinCode);
int User_Set_Default_SmpConfig(unsigned long SmpMode, unsigned long PinCode, unsigned char WithSave);
int User_Set_Default_MTU(unsigned long Mtu, unsigned char WithSave);
int User_Get_Default_MTU(unsigned long *Mtu);
int User_Set_Default_ManuData(unsigned long ManuDataLen, unsigned char *ManuData, unsigned char WithSave);
int User_Get_Default_ManuData(unsigned long *ManuDataLen, unsigned char *ManuData);
int User_Set_Default_SingleSlave(unsigned long SingleSlave, unsigned char WithSave);
int User_Get_Default_SingleSlave(unsigned long *SingleSlave);
int User_Save_Defult_Para(void);
int User_Set_Restore(void);
int User_Set_Reset(void);
int User_Set_BleAdvName(unsigned char *Name, unsigned char length);
int User_Set_BleAdvManuData(unsigned char *ManuData, unsigned short ManuDataLen);
int User_Set_ConnParam(unsigned char Libconidx, unsigned short min_interval, unsigned short max_interval, unsigned short latency, unsigned short timeout);
int User_Set_ConnParam_OnConnected(unsigned char Libconidx);
int User_Set_MTU_OnConnected(unsigned char Libconidx);
int User_Request_MTU(unsigned char ConnIndex);
int User_Get_DataLen(unsigned char ConnIndex);
int User_Adv_Start(void);
int User_Adv_Stop(void);
int User_Set_Flash_Lock(void);
int User_Set_Flash_UnLock(void);
int User_Start_Scanning(unsigned int time_s);
int User_Connect_BLE(unsigned char *BdAddr, unsigned char adv_type, unsigned char ConnIndex, unsigned char AttIndex);
int User_DisConnect_BLE(unsigned char ConnIndex);
int User_Send_BLE(unsigned char ConnIndex, unsigned short Len);
void User_Flash_Protect_Init(void);
int User_Show_Mem(void);
int User_Throughput_Init(void);
int User_Throughput_Stop(void);
unsigned char User_Get_Conn_Libconidx(unsigned char ConnIndex);
unsigned char User_Get_Conn_AttIndex(unsigned char ConnIndex);
int User_Add_Conn_Device(unsigned char *BdAddr, unsigned char Libconidx, unsigned char Role);
unsigned char User_Remove_Conn_By_Libconidx(unsigned char Libconidx);
unsigned char User_Set_Send_Frame_Size(unsigned char Libconidx, unsigned short Size);
unsigned short User_Get_Send_Frame_Size(unsigned char ConnIndex);
unsigned char User_Set_CanSend_Flag(unsigned char Libconidx, unsigned char CanSend);
unsigned char User_Get_CanSend_Flag(unsigned char ConnIndex);
unsigned char User_Get_Conn_Role(unsigned char ConnIndex);
unsigned char User_Get_ConnIndex_By_Libconidx(unsigned char Libconidx);
int User_Get_Conn_Master_BdAddr_Check(unsigned char *BdAddr);
unsigned char User_Get_Conn_Master_Connected_Num(void);
unsigned char User_Get_ConnIndex_Master_NoActive(void);
unsigned char User_Get_ConnIndex_Slave_NoActive(void);
unsigned short User_Get_Now_SendIng_Len(void);
int User_Print_Conn_Info(void);
int User_Print_Conn_Info_CMD(void);
int User_Print_Bond_List_CMD(void);
int User_Conn_For_ADV_Init(void);
int User_Start_Conn_For_ADV_Scan(gap_evt_adv_report_t *adv_rpt);
int User_Start_Conn_For_ADV_Process(unsigned char *BdAddr);
int User_Conn_For_ADV_Connected_Process(void);
int User_Conn_For_ADV_DisConnected_Process(void);
int User_Start_Conn_For_ADV(unsigned char *ManuData, unsigned char ManuLen, unsigned char MaxNum);
int User_Stop_Conn_For_ADV(void);
unsigned char User_Get_Throughput_Pin(void);
int User_Get_Default_ServiceUUID(unsigned short *Service_UUID, unsigned short *Notify_UUID, unsigned short *Write_UUID);
int User_Set_Default_ServiceUUID(unsigned short Service_UUID, unsigned short Notify_UUID, unsigned short Write_UUID, unsigned char WithSave);
void mainloop(void);
void update_last_system_time(void);
void update_multi_system_time(void);
BT_RunMode_t User_Get_BT_RunMode(void);
void User_Set_BT_RunMode(BT_RunMode_t mode);
void generate_mac_addr(void);
int User_Set_Power_Now(unsigned long Power);
unsigned char User_Get_Conn_Num(void);
unsigned char User_Get_Conn_Slave_Connected_Num(void);
int User_Enter_ENTM_Mode(void);
int User_Set_AutoThroughput(unsigned char AutoThroughput);
int User_Get_AutoThroughput(void);
unsigned char set_scan_filter_name(unsigned char *Name, unsigned char length);
unsigned char set_scan_filter_name_flag(bool Nameflag);
int User_Set_ConWakeUp(unsigned char ConWakeUp);
int User_Get_ConWakeUp(void);

int user_set_mac_addr(mac_addr_t *addr);
void generateRandomNum(int len, unsigned char *data);
#endif
