/*
 * @Author: ya<PERSON><PERSON><PERSON>
 * @Date: 2023-06-12 17:53:22
 * @LastEditTime: 2023-10-31 19:21:18
 * @Description: 
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */
/**
  ******************************************************************************
  *
  * Licensed under MCD-ST Liberty SW License Agreement V2, (the "License");
  * You may not use this file except in compliance with the License.
  * You may obtain a copy of the License at:
  *
  *        http://www.st.com/software_license_agreement_liberty_v2
  *
  * Unless required by applicable law or agreed to in writing, software
  * distributed under the License is distributed on an "AS IS" BASIS,
  * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  * See the License for the specific language governing permissions and
  * limitations under the License.
  *
  ******************************************************************************
  */
#include "UserUartUpdata.h"
#include "flash_usage_config.h"
#ifdef YMODEM_START

/* Define to prevent recursive inclusion -------------------------------------*/
#ifndef __YMODEM_H_
#define __YMODEM_H_

/* Includes ------------------------------------------------------------------*/
/* Exported types ------------------------------------------------------------*/
#define PORTING_YMODEM 1  // 1: porting for 8016 0: close
#if PORTING_YMODEM
/* Define the address from where user application will be loaded.
Note: this area is reserved for the IAP code                  */
#define APPLICATION_ADDRESS     (unsigned int)0x00000000      /* ####Start user code address: ADDR_FLASH_PAGE_8 */
/* Define the user application size */
#define USER_FLASH_SIZE               (IMAGE_SIZE) /* Small default template application */
#define TX_TIMEOUT          ((unsigned int)1000)


/** 
  * @brief  HAL Status structures definition  
  */  
typedef enum 
{
  HAL_OK       = 0x00U,
  HAL_ERROR    = 0x01U,
  HAL_BUSY     = 0x02U,
  HAL_TIMEOUT  = 0x03U
} HAL_StatusTypeDef;

#define FLASHIF_OK 0

#define IS_CAP_LETTER(c)    (((c) >= 'A') && ((c) <= 'F'))
#define IS_LC_LETTER(c)     (((c) >= 'a') && ((c) <= 'f'))
#define IS_09(c)            (((c) >= '0') && ((c) <= '9'))
#define ISVALIDHEX(c)       (IS_CAP_LETTER(c) || IS_LC_LETTER(c) || IS_09(c))
#define ISVALIDDEC(c)       IS_09(c)
#define CONVERTDEC(c)       (c - '0')

#define CONVERTHEX_ALPHA(c) (IS_CAP_LETTER(c) ? ((c) - 'A'+10) : ((c) - 'a'+10))
#define CONVERTHEX(c)       (IS_09(c) ? ((c) - '0') : CONVERTHEX_ALPHA(c))

#define __HAL_UART_FLUSH_DRREGISTER(__HANDLE__)  \
  do{   }  while(0U)

#endif

/**
  * @brief  Comm status structures definition
  */
typedef enum
{
    COM_OK       = 0x00,
    COM_ERROR    = 0x01,
    COM_ABORT    = 0x02,
    COM_TIMEOUT  = 0x03,
    COM_DATA     = 0x04,
    COM_LIMIT    = 0x05
} COM_StatusTypeDef;

/* Exported constants --------------------------------------------------------*/
/* Packet structure defines */
#define PACKET_HEADER_SIZE      ((unsigned int)3)
#define PACKET_DATA_INDEX       ((unsigned int)4)
#define PACKET_START_INDEX      ((unsigned int)1)
#define PACKET_NUMBER_INDEX     ((unsigned int)2)
#define PACKET_CNUMBER_INDEX    ((unsigned int)3)
#define PACKET_TRAILER_SIZE     ((unsigned int)2)
#define PACKET_OVERHEAD_SIZE    (PACKET_HEADER_SIZE + PACKET_TRAILER_SIZE - 1)
#define PACKET_SIZE             ((unsigned int)128)
#define PACKET_1K_SIZE          ((unsigned int)1024)

/* /-------- Packet in IAP memory ------------------------------------------\
 * | 0      |  1    |  2     |  3   |  4      | ... | n+4     | n+5  | n+6  |
 * |------------------------------------------------------------------------|
 * | unused | start | number | !num | data[0] | ... | data[n] | crc0 | crc1 |
 * \------------------------------------------------------------------------/
 * the first byte is left unused for memory alignment reasons                 */

#define FILE_NAME_LENGTH        ((unsigned int)64)
#define FILE_SIZE_LENGTH        ((unsigned int)16)

#define SOH                     ((unsigned char)0x01)  /* start of 128-byte data packet */
#define STX                     ((unsigned char)0x02)  /* start of 1024-byte data packet */
#define EOT                     ((unsigned char)0x04)  /* end of transmission */
#define ACK                     ((unsigned char)0x06)  /* acknowledge */
#define NAK                     ((unsigned char)0x15)  /* negative acknowledge */
#define CA                      ((unsigned int)0x18) /* two of these in succession aborts transfer */
#define CRC16                   ((unsigned char)0x43)  /* 'C' == 0x43, request 16-bit CRC */
#define NEGATIVE_BYTE           ((unsigned char)0xFF)

#define ABORT1                  ((unsigned char)0x41)  /* 'A' == 0x41, abort by user */
#define ABORT2                  ((unsigned char)0x61)  /* 'a' == 0x61, abort by user */

#define NAK_TIMEOUT             ((unsigned int)2000) /* tow second retry delay */
#define DOWNLOAD_TIMEOUT        ((unsigned int)1000) /* One second retry delay */
#define MAX_ERRORS              ((unsigned int)2) /* Maximum number of retry */

/* Exported functions ------------------------------------------------------- */
COM_StatusTypeDef Ymodem_Receive(unsigned int *p_size);
COM_StatusTypeDef Ymodem_Transmit(unsigned char *p_buf, const unsigned char *p_file_name, unsigned int file_size);
void wlt_uart_ymodem(void);

void ymodem_set_flag(unsigned char flag);
unsigned char ymodem_get_flag(void);
#endif  /* __YMODEM_H_ */

#endif // YMODEM_START
