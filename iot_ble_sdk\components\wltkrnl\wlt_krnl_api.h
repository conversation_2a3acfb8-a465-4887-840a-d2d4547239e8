/**
 * Copyright (c) 2021, wi-linktech
 * 
 * All rights reserved.
 * 
 * 
 */
#ifndef __WLT_KRNL_API__
#define __WLT_KRNL_API__

#include "wlt_ble_type.h"
/* Miscellaneous Type definitions that should already be defined,    */
/* but are necessary.                                                */
#ifndef NULL
#define NULL ((void *)0)
#endif

#ifndef TRUE
#define TRUE (1 == 1)
#endif

#ifndef FALSE
#define FALSE (0 == 1)
#endif

/* The following constant represents the Minimum Amount of Time      */
/* that can be scheduled with the WLT Scheduler.  Attempting to     */
/* Schedule a Scheduled Function less than this time will result in  */
/* the function being scheduled for the specified Amount.  This      */
/* value is in Milliseconds.                                         */
#define WLT_MINIMUM_SCHEDULER_RESOLUTION (0)

#define WLT_INITIALIZATION_SIZE (sizeof(WLT_Initialization_t))

/* The following structure represents the statistics for the memory  */
/* heap for use with WLT_QueryMemoryUsage().                        */
// typedef struct _tagWLT_MemoryStatistics_t
// {
//   unsigned int HeapSize;
//   unsigned int CurrentHeapUsed;
//   unsigned int MaximumHeapUsed;
//   unsigned int FreeFragmentCount;
//   unsigned int LargestFreeFragment;
// } WLT_MemoryStatistics_t;

/* The following function is responsible for the Memory Usage        */
/* Information.  This function accepts as its parameter a pointer to */
/* a memory statistics structure and a flag to indicate if fragment  */
/* information will be determined.  The function will return zero if */
/* successful or a negative value if there is an error.              */
/* * NOTE * If the advanced statitistics flag is set to FALSE, then  */
/*          the largest free fragment and free fragment count will be*/
/*          set to zero.                                             */
uint16_t wlt_query_free_feap_size(void);

/* The following type definition defines a WLT Kernel API Mailbox   */
/* Handle.                                                           */
typedef void *mailbox_t;

/* The following MACRO is a utility MACRO that exists to calculate   */
/* the offset position of a particular structure member from the     */
/* start of the structure.  This MACRO accepts as the first          */
/* parameter, the physical name of the structure (the type name, NOT */
/* the variable name).  The second parameter to this MACRO represents*/
/* the actual structure member that the offset is to be determined.  */
/* This MACRO returns an unsigned integer that represents the offset */
/* (in bytes) of the structure member.                               */
#define WLT_STRUCTURE_OFFSET(_x, _y) ((unsigned int)&(((_x *)0)->_y))

/* The following type declaration represents the Prototype for the   */
/* function that is passed to the WLT_DeleteMailbox() function to   */
/* process all remaining Queued Mailbox Messages.  This allows a     */
/* mechanism to free any resources that are attached with each queued*/
/* Mailbox message.                                                  */
typedef void (*wlt_mailbox_delete_callback_t)(void *mailbox_data);

/* The following type declaration represents the Prototype for a     */
/* Scheduler Function.  This function represents the Function that   */
/* will be executed periodically when passed to the                  */
/* WLT_AddFunctionToScheduler() function.                           */
/* * NOTE * The ScheduleParameter is the same parameter value that   */
/*          was passed to the WLT_AddFunctionToScheduler() when     */
/*          the function was added to the scheduler.                 */
/* * NOTE * Once a Function is added to the Scheduler there is NO    */
/*          way to remove it.                                        */
typedef void (*wlt_scheduler_function_t)(void *schedule_parameter);

/* The following function is responsible for delaying the current    */
/* task for the specified duration (specified in Milliseconds).      */
/* * NOTE * Very small timeouts might be smaller in granularity than */
/*          the system can support !!!!                              */
void wlt_delay(unsigned long milliSeconds);

/* The following function is responsible for retrieving the current  */
/* Tick Count of system.  This function returns the System Tick      */
/* Count in Milliseconds resolution.                                 */
uint32_t wlt_get_tick_count(void);

/* The following function is provided to allow a mechanism for       */
/* adding Scheduler Functions to the Scheduler.  These functions are */
/* called periodically by the Scheduler (based upon the requested    */
/* Schedule Period).  This function accepts as input the Scheduler   */
/* Function to add to the Scheduler, the Scheduler parameter that is */
/* passed to the Scheduled function, and the Scheduler Period.  The  */
/* Scheduler Period is specified in Milliseconds.  This function     */
/* returns TRUE if the function was added successfully or FALSE if   */
/* there was an error.                                               */
/* * NOTE * Once a function is added to the Scheduler, it can only   */
/*          be removed by calling the                                */
/*          WLT_DeleteFunctionFromScheduler() function.             */
/* * NOTE * The WLT_ExecuteScheduler() function *MUST* be called    */
/*          ONCE (AND ONLY ONCE) to begin the Scheduler Executing    */
/*          periodic Scheduled functions (or calling the             */
/*          WLT_ProcessScheduler() function repeatedly.             */
bool wlt_add_function_to_scheduler(wlt_scheduler_function_t scheduler_function, void *scheduler_parameter, unsigned int period);

/* The following function is provided to allow a mechanism for       */
/* deleting a Function that has previously been registered with the  */
/* Scheduler via a successful call to the                            */
/* WLT_AddFunctionToScheduler() function.  This function accepts as */
/* input the Scheduler Function to that was added to the Scheduler,  */
/* as well as the Scheduler Parameter that was registered.  Both of  */
/* these values *must* match to remove a specific Scheduler Entry.   */
void wlt_delete_function_from_scheduler(wlt_scheduler_function_t scheduler_function, void *scheduler_parameter);

/* The following function begins execution of the actual Scheduler.  */
/* Once this function is called, it NEVER returns.  This function is */
/* responsible for executing all functions that have been added to   */
/* the Scheduler with the WLT_AddFunctionToScheduler() function.    */
void wlt_execute_scheduler(void);

/* The following function is provided to allow a mechanism to process*/
/* the scheduled functions in the scheduler.  This function performs */
/* the same functionality as the WLT_ExecuteScheduler() function    */
/* except that it returns as soon as it has made an iteration through*/
/* the scheduled functions.  This function is provided for platforms */
/* that would like to implement their own processing loop and/or     */
/* scheduler and not rely on the Bluetopia implementation.           */
/* * NOTE * This function should NEVER be called if the              */
/*          WLT_ExecuteScheduler() schema is used.                  */
/* * NOTE * This function *MUST* not be called from any scheduled    */
/*          function that was added to the scheduler via the         */
/*          WLT_AddFunctionToScheduler() function or an infinite    */
/*          loop will occur.                                         */
void wlt_process_scheduler(void);

/* The following function is provided to allow a mechanism to        */
/* actually allocate a Block of Memory (of at least the specified    */
/* size).  This function accepts as input the size (in Bytes) of the */
/* Block of Memory to be allocated.  This function returns a NON-NULL*/
/* pointer to this Memory Buffer if the Memory was successfully      */
/* allocated, or a NULL value if the memory could not be allocated.  */
void *wlt_allocate_memory(unsigned long memory_size);

/* The following function is responsible for de-allocating a Block of*/
/* Memory that was successfully allocated with the                   */
/* WLT_AllocateMemory() function.  This function accepts a NON-NULL */
/* Memory Pointer which was returned from the WLT_AllocateMemory()  */
/* function.  After this function completes the caller CANNOT use ANY*/
/* of the Memory pointed to by the Memory Pointer.                   */
void wlt_free_memory(void *memory_pointer);

// /* The following function is responsible for copying a block of      */
// /* memory of the specified size from the specified source pointer to */
// /* the specified destination memory pointer.  This function accepts  */
// /* as input a pointer to the memory block that is to be Destination  */
// /* Buffer (first parameter), a pointer to memory block that points to*/
// /* the data to be copied into the destination buffer, and the size   */
// /* (in bytes) of the Data to copy.  The Source and Destination Memory*/
// /* Buffers must contain AT LEAST as many bytes as specified by the   */
// /* Size parameter.                                                   */
// /* * NOTE * This function does not allow the overlapping of the      */
// /*          Source and Destination Buffers !!!!                      */
// void WLT_MemCopy(void *Destination, const void *Source, unsigned long Size);

// /* The following function is responsible for moving a block of memory*/
// /* of the specified size from the specified source pointer to the    */
// /* specified destination memory pointer.  This function accepts as   */
// /* input a pointer to the memory block that is to be Destination     */
// /* Buffer (first parameter), a pointer to memory block that points to*/
// /* the data to be copied into the destination buffer, and the size   */
// /* (in bytes) of the Data to copy.  The Source and Destination Memory*/
// /* Buffers must contain AT LEAST as many bytes as specified by the   */
// /* Size parameter.                                                   */
// /* * NOTE * This function DOES allow the overlapping of the Source   */
// /*          and Destination Buffers.                                 */
// void WLT_MemMove(void *Destination, const void *Source, unsigned long Size);

// /* The following function is provided to allow a mechanism to fill a */
// /* block of memory with the specified value.  This function accepts  */
// /* as input a pointer to the Data Buffer (first parameter) that is to*/
// /* filled with the specified value (second parameter).  The final    */
// /* parameter to this function specifies the number of bytes that are */
// /* to be filled in the Data Buffer.  The Destination Buffer must     */
// /* point to a Buffer that is AT LEAST the size of the Size parameter.*/
// void wlt_memset(void *Destination, unsigned char Value, unsigned long Size);

// /* The following function is provided to allow a mechanism to Compare*/
// /* two blocks of memory to see if the two memory blocks (each of size*/
// /* Size (in bytes)) are equal (each and every byte up to Size bytes).*/
// /* This function returns a negative number if Source1 is less than   */
// /* Source2, zero if Source1 equals Source2, and a positive value if  */
// /* Source1 is greater than Source2.                                  */
// int wlt_memcmp(const void *Source1, const void *Source2, unsigned long Size);

// /* The following function is provided to allow a mechanism to Compare*/
// /* two blocks of memory to see if the two memory blocks (each of size*/
// /* Size (in bytes)) are equal (each and every byte up to Size bytes) */
// /* using a Case-Insensitive Compare.  This function returns a        */
// /* negative number if Source1 is less than Source2, zero if Source1  */
// /* equals Source2, and a positive value if Source1 is greater than   */
// /* Source2.                                                          */
// int wlt_memcmp1(const void *Source1, const void *Source2, unsigned long Size);

// /* The following function is provided to allow a mechanism to copy a */
// /* source NULL Terminated ASCII (character) String to the specified  */
// /* Destination String Buffer.  This function accepts as input a      */
// /* pointer to a buffer (Destination) that is to receive the NULL     */
// /* Terminated ASCII String pointed to by the Source parameter.  This */
// /* function copies the string byte by byte from the Source to the    */
// /* Destination (including the NULL terminator).                      */
// void wlt_string_copy(char *Destination, const char *Source);

// /* The following function is provided to allow a mechanism to        */
// /* determine the Length (in characters) of the specified NULL        */
// /* Terminated ASCII (character) String.  This function accepts as    */
// /* input a pointer to a NULL Terminated ASCII String and returns the */
// /* number of characters present in the string (NOT including the     */
// /* terminating NULL character).                                      */
// unsigned int wlt_string_length(const char *Source);

// /* The following function is provided to allow a mechanism for a C   */
// /* Run-Time Library sprintf() function implementation.  This function*/
// /* accepts as its imput the output buffer, a format string and a     */
// /* variable number of arguments determined by the format string.     */
// int wlt_sprintf(char *Buffer, const char *Format, ...);

/* The following function is provided to allow a mechanism to create */
/* a Mailbox.  A Mailbox is a Data Store that contains slots (all of */
/* the same size) that can have data placed into (and retrieved      */
/* from).  Once Data is placed into a Mailbox (via the               */
/* WLT_AddMailbox() function, it can be retrieved by using the      */
/* WLT_WaitMailbox() function.  Data placed into the Mailbox is     */
/* retrieved in a FIFO method.  This function accepts as input the   */
/* Maximum Number of Slots that will be present in the Mailbox and   */
/* the Size of each of the Slots.  This function returns a NON-NULL  */
/* Mailbox Handle if the Mailbox is successfully created, or a NULL  */
/* Mailbox Handle if the Mailbox was unable to be created.           */
mailbox_t wlt_create_mailbox(unsigned int number_slots, unsigned int slot_size);

/* The following function is provided to allow a means to Add data to*/
/* the Mailbox (where it can be retrieved via the WLT_WaitMailbox() */
/* function.  This function accepts as input the Mailbox Handle of   */
/* the Mailbox to place the data into and a pointer to a buffer that */
/* contains the data to be added.  This pointer *MUST* point to a    */
/* data buffer that is AT LEAST the Size of the Slots in the Mailbox */
/* (specified when the Mailbox was created) and this pointer CANNOT  */
/* be NULL.  The data that the MailboxData pointer points to is      */
/* placed into the Mailbox where it can be retrieved via the         */
/* WLT_WaitMailbox() function.                                      */
/* * NOTE * This function copies from the MailboxData Pointer the    */
/*          first SlotSize Bytes.  The SlotSize was specified when   */
/*          the Mailbox was created via a successful call to the     */
/*          WLT_CreateMailbox() function.                           */
bool wlt_add_mailbox(mailbox_t mailbox, void *mailbox_data);

/* The following function is provided to allow a means to retrieve   */
/* data from the specified Mailbox.  This function will block until  */
/* either Data is placed in the Mailbox or an error with the Mailbox */
/* was detected.  This function accepts as its first parameter a     */
/* Mailbox Handle that represents the Mailbox to wait for the data   */
/* with.  This function accepts as its second parameter, a pointer to*/
/* a data buffer that is AT LEAST the size of a single Slot of the   */
/* Mailbox (specified when the WLT_CreateMailbox() function was     */
/* called).  The MailboxData parameter CANNOT be NULL.  This function*/
/* will return TRUE if data was successfully retrieved from the      */
/* Mailbox or FALSE if there was an error retrieving data from the   */
/* Mailbox.  If this function returns TRUE then the first SlotSize   */
/* bytes of the MailboxData pointer will contain the data that was   */
/* retrieved from the Mailbox.                                       */
/* * NOTE * This function copies to the MailboxData Pointer the data */
/*          that is present in the Mailbox Slot (of size SlotSize).  */
/*          The SlotSize was specified when the Mailbox was created  */
/*          via a successful call to the WLT_CreateMailbox()        */
/*          function.                                                */
bool wlt_wait_mailbox(mailbox_t mailbox, void *mailbox_data);

/* The following function is a utility function that exists to       */
/* determine if there is anything queued in the specified Mailbox.   */
/* This function returns TRUE if there is something queued in the    */
/* Mailbox, or FALSE if there is nothing queued in the specified     */
/* Mailbox.                                                          */
bool wlt_query_mailbox(mailbox_t mailbox);

/* The following function is responsible for destroying a Mailbox    */
/* that was created successfully via a successful call to the        */
/* WLT_CreateMailbox() function.  This function accepts as input the*/
/* Mailbox Handle of the Mailbox to destroy.  Once this function is  */
/* completed the Mailbox Handle is NO longer valid and CANNOT be     */
/* used.  Calling this function will cause all outstanding           */
/* WLT_WaitMailbox() functions to fail with an error.  The final    */
/* parameter specifies an (optional) callback function that is called*/
/* for each queued Mailbox entry.  This allows a mechanism to free   */
/* any resources that might be associated with each individual       */
/* Mailbox item.                                                     */
void wlt_delete_mailbox(mailbox_t mailbox, wlt_mailbox_delete_callback_t mailbox_delete_callback);

/* The following function is used to initialize the Platform module. */
/* The Platform module relies on some static variables that are used */
/* to coordinate the abstraction.  When the module is initially      */
/* started from a cold boot, all variables are set to the proper     */
/* state.  If the Warm Boot is required, then these variables need to*/
/* be reset to their default values.  This function sets all static  */
/* parameters to their default values.                               */
/* * NOTE * The implementation is free to pass whatever information  */
/*          required in this parameter.                              */
void wlt_system_init(void *user_param);

/* The following function is used to cleanup the Platform module.    */
void wlt_system_deInit(void);

void wlt_systick_handler(void);

#endif
