/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:48
 * @LastEditTime: 2023-10-22 16:43:03
 * @Description: This file is used to define macro definition.
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */


#ifndef _USER_APPCONFIG_H
#define _USER_APPCONFIG_H
#include "driver_system.h"

#define VERSION_NAME "WLT8016"
#define VERSION_CODE "V1.02.22"
#define HARDWARE_VERSION_CODE "V1.0.0"
#define MANU_NUMBER "WLT"

#define WATCHDOG_SECOND 3 //看门狗时间3S

#define USER_PARAMETER_KV_KEY "user_parameter"

//默认用户参数
#define USER_DEFAULT_PARA_BAUDRATE              115200 //波特率
#define USER_DEFAULT_PARA_POWER                 RF_TX_POWER_POS_10dBm //发射功率
#define USER_DEFAULT_PARA_ADV_INTERVAL_MIN      48 //广播间隔最小值
#define USER_DEFAULT_PARA_ADV_INTERVAL_MAX      88 //广播间隔最大值
#define USER_DEFAULT_PARA_CONNPARA_UPDATE       1 //是否允许连接参数更新
#define USER_DEFAULT_PARA_CONNPARA_INTERVAL_MIN 8 //连接间隔最小值
#define USER_DEFAULT_PARA_CONNPARA_INTERVAL_MAX 12 //连接间隔最大值
#define USER_DEFAULT_PARA_CONNPARA_LATENCY      0 //从机延迟
#define USER_DEFAULT_PARA_CONNPARA_TIMEOUT      200 //连接超时
#define USER_DEFAULT_PARA_SMP_PARING_MODE       1 //使用SMP配对模式
#define USER_DEFAULT_PARA_SMP_PINCODE           123456 //SMP配对密码
#define USER_DEFAULT_PARA_MTU_SIZE              259 //MTU大小
#define USER_DEFAULT_PARA_ROLE                  0 //0为主从角色，1为单从角色
#define USER_DEFAULT_PARA_SERVICE_UUID          0xFFF0 //服务UUID
#define USER_DEFAULT_PARA_NOTIFY_UUID           0xFFF1 //通知UUID
#define USER_DEFAULT_PARA_WRITE_UUID            0xFFF2 //写UUID
#define USER_DEFAULT_PARA_AUTOTHROUGHPUT        1 //是否自动进入透传模式，1为自动进入，0为不自动进入
#define USER_DEFAULT_PARA_CONWAKEUP             1 //是否连接唤醒，1为连接唤醒，0为不连接唤醒

//多连接数量
#define MASTER_MAX_NUM 1 //主机最大连接数量
#define SLAVE_MAX_NUM 1 //从机最大连接数量
#define USER_BLMS_MAX_CONN_NUM (MASTER_MAX_NUM + SLAVE_MAX_NUM) //最大连接数量

//透传分包超时时间
#define USER_THROUGHPUT_TIMEOUT 50 //单位ms

//多连接模式，退出继续等待数据超时时间
#define USER_MULTI_THROUGHPUT_TIMEOUT 200 //单位ms

//剩余可用RAM大小
#define USER_FREE_RAM_SIZE    1024*4 //单位byte

//做主扫描参数
#define USER_REPORT_DATA_LENGTH     50          //做主扫描重复过滤保存数量
#define USER_AT_SCAN_TIME_MIN       1           //单位s
#define USER_AT_SCAN_TIME_MAX       100         //单位s
#endif
