__ARM_use_no_argv = 0x00000000;
__Vectors = 0x00000000;
__main = 0x0000008d;
_main_stk = 0x0000008d;
_main_scatterload = 0x00000091;
__main_after_scatterload = 0x00000095;
_main_clock = 0x00000095;
_main_cpp_init = 0x00000095;
_main_init = 0x00000095;
__rt_final_cpp = 0x0000009d;
__rt_final_exit = 0x0000009d;
rwip_sleep_mul_64 = 0x000000a1;
__asm___11_low_power_c_50b0e29e__low_power_save_cpu = 0x000000ad;
low_power_restore_cpu = 0x0000012d;
SVC_Handler = 0x00000139;
vPortSVCHandler = 0x00000171;
__asm___6_port_c_39a90d8d__prvStartFirstTask = 0x00000195;
prv_call_svc_pc = 0x000001a9;
PendSV_Handler = 0x000001ad;
vPortGetIPSR = 0x000001f9;
__asm___10_app_boot_c_fcf06f29____REV16 = 0x00000201;
__asm___14_ble_util_buf_c_b8bf6b0e____REV16 = 0x00000205;
__asm___7_rwble_c_5e66ef03____REV16 = 0x00000209;
__asm___5_llc_c_llc_init____REV16 = 0x0000020d;
__asm___15_llc_chmap_upd_c_ddac89ec____REV16 = 0x00000211;
__asm___13_llc_con_upd_c_de71abdd____REV16 = 0x00000215;
__asm___9_llc_dbg_c____REV16 = 0x00000219;
__asm___16_llc_disconnect_c_67e9d540____REV16 = 0x0000021d;
__asm___12_llc_dl_upd_c_553d3f21____REV16 = 0x00000221;
__asm___13_llc_encrypt_c_8159a054____REV16 = 0x00000225;
__asm___15_llc_feat_exch_c_6fa097ef____REV16 = 0x00000229;
__asm___9_llc_hci_c_996dc4b6____REV16 = 0x0000022d;
__asm___13_llc_le_ping_c_434cc73c____REV16 = 0x00000231;
__asm___10_llc_llcp_c_1a9b13a9____REV16 = 0x00000235;
__asm___13_llc_phy_upd_c_0294cefd____REV16 = 0x00000239;
__asm___10_llc_task_c_30369c55____REV16 = 0x0000023d;
__asm___14_llc_ver_exch_c_2771d0a7____REV16 = 0x00000241;
__asm___5_lld_c_f7832d1e____REV16 = 0x00000245;
__asm___9_lld_adv_c_9a148cc9____REV16 = 0x00000249;
__asm___9_lld_con_c_df553fb8____REV16 = 0x0000024d;
__asm___10_lld_init_c_b391185e____REV16 = 0x00000251;
__asm___13_lld_per_adv_c_77b63e9a____REV16 = 0x00000255;
__asm___10_lld_scan_c_01456b52____REV16 = 0x00000259;
__asm___10_lld_sync_c_8bd34c56____REV16 = 0x0000025d;
__asm___10_lld_test_c_54e5e101____REV16 = 0x00000261;
__asm___5_llm_c_52f69136____REV16 = 0x00000265;
__asm___9_llm_adv_c_2ca465c6____REV16 = 0x00000269;
__asm___9_llm_hci_c_20aee1fa____REV16 = 0x0000026d;
__asm___10_llm_init_c_c210a516____REV16 = 0x00000271;
__asm___9_llm_iso_c____REV16 = 0x00000275;
__asm___10_llm_scan_c_963e4937____REV16 = 0x00000279;
__asm___10_llm_task_c_5d3df270____REV16 = 0x0000027d;
__asm___5_hci_c_hci_init____REV16 = 0x00000281;
__asm___8_hci_fc_c_fcf3c82e____REV16 = 0x00000285;
__asm___9_hci_msg_c_b5066cf5____REV16 = 0x00000289;
__asm___8_hci_tl_c_9ce329a5____REV16 = 0x0000028d;
__asm___11_sch_alarm_c_3b4969f2____REV16 = 0x00000291;
__asm___9_sch_arb_c_87561c0e____REV16 = 0x00000295;
__asm___10_sch_plan_c_c0257ea0____REV16 = 0x00000299;
__asm___10_sch_prog_c_de24772c____REV16 = 0x0000029d;
__asm___11_sch_slice_c_de6dd3b5____REV16 = 0x000002a1;
__asm___5_aes_c_aes_init____REV16 = 0x000002a5;
__asm___9_aes_ccm_c_aes_ccm____REV16 = 0x000002a9;
__asm___10_aes_cmac_c_fe47f34c____REV16 = 0x000002ad;
__asm___8_aes_k1_c_aes_k1____REV16 = 0x000002b1;
__asm___8_aes_k2_c_5cc4f012____REV16 = 0x000002b5;
__asm___8_aes_k3_c_979823b7____REV16 = 0x000002b9;
__asm___8_aes_k4_c_8a9d130f____REV16 = 0x000002bd;
__asm___8_aes_s1_c_aes_s1____REV16 = 0x000002c1;
__asm___9_co_list_c_ca99e89f____REV16 = 0x000002c5;
__asm___10_co_utils_c_one_bits____REV16 = 0x000002c9;
__asm___5_dbg_c_3a87ab48____REV16 = 0x000002cd;
__asm___12_dbg_mwsgen_c____REV16 = 0x000002d1;
__asm___12_dbg_swdiag_c____REV16 = 0x000002d5;
__asm___10_dbg_task_c____REV16 = 0x000002d9;
__asm___9_dbg_trc_c____REV16 = 0x000002dd;
__asm___13_dbg_trc_mem_c____REV16 = 0x000002e1;
__asm___12_dbg_trc_tl_c____REV16 = 0x000002e5;
__asm___9_display_c____REV16 = 0x000002e9;
__asm___14_display_task_c____REV16 = 0x000002ed;
__asm___10_ecc_p256_c_8b173798____REV16 = 0x000002f1;
__asm___6_h4tl_c_3a5732fe____REV16 = 0x000002f5;
__asm___4_ke_c_ke_init____REV16 = 0x000002f9;
__asm___10_ke_event_c_87ccd12c____REV16 = 0x000002fd;
__asm___8_ke_mem_c_f92e48dd____REV16 = 0x00000301;
__asm___8_ke_msg_c_d502b297____REV16 = 0x00000305;
__asm___10_ke_queue_c_5cab850e____REV16 = 0x00000309;
__asm___9_ke_task_c_18478d77____REV16 = 0x0000030d;
__asm___10_ke_timer_c_3b621a08____REV16 = 0x00000311;
__asm___6_nvds_c____REV16 = 0x00000315;
__asm___9_rf_simu_c_82de3823____REV16 = 0x00000319;
__asm___6_rwip_c_f2b147fa____REV16 = 0x0000031d;
__asm___13_rwip_driver_c_c12c84cf____REV16 = 0x00000321;
__asm___11_co_printf_c_fputc____REV16 = 0x00000325;
__asm___11_low_power_c_50b0e29e____REV16 = 0x00000329;
__asm___7_crc32_c_36f7c2a5____REV16 = 0x0000032d;
__asm___5_md5_c_MD5_Init____REV16 = 0x00000331;
__asm___11_arch_main_c_uart_api____REV16 = 0x00000335;
__asm___12_jump_table_c_6a9c728e____REV16 = 0x00000339;
__asm___6_intc_c_189c7b66____REV16 = 0x0000033d;
__asm___6_uart_c_988f2830____REV16 = 0x00000341;
__asm___7_flash_c_1f9d869b____REV16 = 0x00000345;
__asm___6_qspi_c_79b82d8e____REV16 = 0x00000349;
__asm___8_system_c_a51710b5____REV16 = 0x0000034d;
__asm___6_gpio_c_a47c3f18____REV16 = 0x00000351;
__asm___5_ssp_c_3d1cf05e____REV16 = 0x00000355;
__asm___8_frspim_c_6e8f94f7____REV16 = 0x00000359;
__asm___5_pmu_c_7ed478f1____REV16 = 0x0000035d;
__asm___9_apb2spi_c_a124e987____REV16 = 0x00000361;
__asm___6_trng_c_36387bb2____REV16 = 0x00000365;
__asm___6_list_c_a968f7cb____REV16 = 0x00000369;
__asm___7_queue_c_48e2f297____REV16 = 0x0000036d;
__asm___7_tasks_c_f31043e3____REV16 = 0x00000371;
__asm___8_timers_c_a20bbafd____REV16 = 0x00000375;
__asm___14_event_groups_c_5f98c4ce____REV16 = 0x00000379;
__asm___8_heap_6_c_94e30ff9____REV16 = 0x0000037d;
__asm___6_port_c_39a90d8d____REV16 = 0x00000381;
__asm___20_freertos_interface_c_bc2b671d____REV16 = 0x00000385;
__asm___10_app_boot_c_fcf06f29____REVSH = 0x00000389;
__asm___14_ble_util_buf_c_b8bf6b0e____REVSH = 0x0000038d;
__asm___7_rwble_c_5e66ef03____REVSH = 0x00000391;
__asm___5_llc_c_llc_init____REVSH = 0x00000395;
__asm___15_llc_chmap_upd_c_ddac89ec____REVSH = 0x00000399;
__asm___13_llc_con_upd_c_de71abdd____REVSH = 0x0000039d;
__asm___9_llc_dbg_c____REVSH = 0x000003a1;
__asm___16_llc_disconnect_c_67e9d540____REVSH = 0x000003a5;
__asm___12_llc_dl_upd_c_553d3f21____REVSH = 0x000003a9;
__asm___13_llc_encrypt_c_8159a054____REVSH = 0x000003ad;
__asm___15_llc_feat_exch_c_6fa097ef____REVSH = 0x000003b1;
__asm___9_llc_hci_c_996dc4b6____REVSH = 0x000003b5;
__asm___13_llc_le_ping_c_434cc73c____REVSH = 0x000003b9;
__asm___10_llc_llcp_c_1a9b13a9____REVSH = 0x000003bd;
__asm___13_llc_phy_upd_c_0294cefd____REVSH = 0x000003c1;
__asm___10_llc_task_c_30369c55____REVSH = 0x000003c5;
__asm___14_llc_ver_exch_c_2771d0a7____REVSH = 0x000003c9;
__asm___5_lld_c_f7832d1e____REVSH = 0x000003cd;
__asm___9_lld_adv_c_9a148cc9____REVSH = 0x000003d1;
__asm___9_lld_con_c_df553fb8____REVSH = 0x000003d5;
__asm___10_lld_init_c_b391185e____REVSH = 0x000003d9;
__asm___13_lld_per_adv_c_77b63e9a____REVSH = 0x000003dd;
__asm___10_lld_scan_c_01456b52____REVSH = 0x000003e1;
__asm___10_lld_sync_c_8bd34c56____REVSH = 0x000003e5;
__asm___10_lld_test_c_54e5e101____REVSH = 0x000003e9;
__asm___5_llm_c_52f69136____REVSH = 0x000003ed;
__asm___9_llm_adv_c_2ca465c6____REVSH = 0x000003f1;
__asm___9_llm_hci_c_20aee1fa____REVSH = 0x000003f5;
__asm___10_llm_init_c_c210a516____REVSH = 0x000003f9;
__asm___9_llm_iso_c____REVSH = 0x000003fd;
__asm___10_llm_scan_c_963e4937____REVSH = 0x00000401;
__asm___10_llm_task_c_5d3df270____REVSH = 0x00000405;
__asm___5_hci_c_hci_init____REVSH = 0x00000409;
__asm___8_hci_fc_c_fcf3c82e____REVSH = 0x0000040d;
__asm___9_hci_msg_c_b5066cf5____REVSH = 0x00000411;
__asm___8_hci_tl_c_9ce329a5____REVSH = 0x00000415;
__asm___11_sch_alarm_c_3b4969f2____REVSH = 0x00000419;
__asm___9_sch_arb_c_87561c0e____REVSH = 0x0000041d;
__asm___10_sch_plan_c_c0257ea0____REVSH = 0x00000421;
__asm___10_sch_prog_c_de24772c____REVSH = 0x00000425;
__asm___11_sch_slice_c_de6dd3b5____REVSH = 0x00000429;
__asm___5_aes_c_aes_init____REVSH = 0x0000042d;
__asm___9_aes_ccm_c_aes_ccm____REVSH = 0x00000431;
__asm___10_aes_cmac_c_fe47f34c____REVSH = 0x00000435;
__asm___8_aes_k1_c_aes_k1____REVSH = 0x00000439;
__asm___8_aes_k2_c_5cc4f012____REVSH = 0x0000043d;
__asm___8_aes_k3_c_979823b7____REVSH = 0x00000441;
__asm___8_aes_k4_c_8a9d130f____REVSH = 0x00000445;
__asm___8_aes_s1_c_aes_s1____REVSH = 0x00000449;
__asm___9_co_list_c_ca99e89f____REVSH = 0x0000044d;
__asm___10_co_utils_c_one_bits____REVSH = 0x00000451;
__asm___5_dbg_c_3a87ab48____REVSH = 0x00000455;
__asm___12_dbg_mwsgen_c____REVSH = 0x00000459;
__asm___12_dbg_swdiag_c____REVSH = 0x0000045d;
__asm___10_dbg_task_c____REVSH = 0x00000461;
__asm___9_dbg_trc_c____REVSH = 0x00000465;
__asm___13_dbg_trc_mem_c____REVSH = 0x00000469;
__asm___12_dbg_trc_tl_c____REVSH = 0x0000046d;
__asm___9_display_c____REVSH = 0x00000471;
__asm___14_display_task_c____REVSH = 0x00000475;
__asm___10_ecc_p256_c_8b173798____REVSH = 0x00000479;
__asm___6_h4tl_c_3a5732fe____REVSH = 0x0000047d;
__asm___4_ke_c_ke_init____REVSH = 0x00000481;
__asm___10_ke_event_c_87ccd12c____REVSH = 0x00000485;
__asm___8_ke_mem_c_f92e48dd____REVSH = 0x00000489;
__asm___8_ke_msg_c_d502b297____REVSH = 0x0000048d;
__asm___10_ke_queue_c_5cab850e____REVSH = 0x00000491;
__asm___9_ke_task_c_18478d77____REVSH = 0x00000495;
__asm___10_ke_timer_c_3b621a08____REVSH = 0x00000499;
__asm___6_nvds_c____REVSH = 0x0000049d;
__asm___9_rf_simu_c_82de3823____REVSH = 0x000004a1;
__asm___6_rwip_c_f2b147fa____REVSH = 0x000004a5;
__asm___13_rwip_driver_c_c12c84cf____REVSH = 0x000004a9;
__asm___11_co_printf_c_fputc____REVSH = 0x000004ad;
__asm___11_low_power_c_50b0e29e____REVSH = 0x000004b1;
__asm___7_crc32_c_36f7c2a5____REVSH = 0x000004b5;
__asm___5_md5_c_MD5_Init____REVSH = 0x000004b9;
__asm___11_arch_main_c_uart_api____REVSH = 0x000004bd;
__asm___12_jump_table_c_6a9c728e____REVSH = 0x000004c1;
__asm___6_intc_c_189c7b66____REVSH = 0x000004c5;
__asm___6_uart_c_988f2830____REVSH = 0x000004c9;
__asm___7_flash_c_1f9d869b____REVSH = 0x000004cd;
__asm___6_qspi_c_79b82d8e____REVSH = 0x000004d1;
__asm___8_system_c_a51710b5____REVSH = 0x000004d5;
__asm___6_gpio_c_a47c3f18____REVSH = 0x000004d9;
__asm___5_ssp_c_3d1cf05e____REVSH = 0x000004dd;
__asm___8_frspim_c_6e8f94f7____REVSH = 0x000004e1;
__asm___5_pmu_c_7ed478f1____REVSH = 0x000004e5;
__asm___9_apb2spi_c_a124e987____REVSH = 0x000004e9;
__asm___6_trng_c_36387bb2____REVSH = 0x000004ed;
__asm___6_list_c_a968f7cb____REVSH = 0x000004f1;
__asm___7_queue_c_48e2f297____REVSH = 0x000004f5;
__asm___7_tasks_c_f31043e3____REVSH = 0x000004f9;
__asm___8_timers_c_a20bbafd____REVSH = 0x000004fd;
__asm___14_event_groups_c_5f98c4ce____REVSH = 0x00000501;
__asm___8_heap_6_c_94e30ff9____REVSH = 0x00000505;
__asm___6_port_c_39a90d8d____REVSH = 0x00000509;
__asm___20_freertos_interface_c_bc2b671d____REVSH = 0x0000050d;
__asm___10_app_boot_c_fcf06f29____RRX = 0x00000511;
__asm___14_ble_util_buf_c_b8bf6b0e____RRX = 0x00000519;
__asm___7_rwble_c_5e66ef03____RRX = 0x00000521;
__asm___5_llc_c_llc_init____RRX = 0x00000529;
__asm___15_llc_chmap_upd_c_ddac89ec____RRX = 0x00000531;
__asm___13_llc_con_upd_c_de71abdd____RRX = 0x00000539;
__asm___9_llc_dbg_c____RRX = 0x00000541;
__asm___16_llc_disconnect_c_67e9d540____RRX = 0x00000549;
__asm___12_llc_dl_upd_c_553d3f21____RRX = 0x00000551;
__asm___13_llc_encrypt_c_8159a054____RRX = 0x00000559;
__asm___15_llc_feat_exch_c_6fa097ef____RRX = 0x00000561;
__asm___9_llc_hci_c_996dc4b6____RRX = 0x00000569;
__asm___13_llc_le_ping_c_434cc73c____RRX = 0x00000571;
__asm___10_llc_llcp_c_1a9b13a9____RRX = 0x00000579;
__asm___13_llc_phy_upd_c_0294cefd____RRX = 0x00000581;
__asm___10_llc_task_c_30369c55____RRX = 0x00000589;
__asm___14_llc_ver_exch_c_2771d0a7____RRX = 0x00000591;
__asm___5_lld_c_f7832d1e____RRX = 0x00000599;
__asm___9_lld_adv_c_9a148cc9____RRX = 0x000005a1;
__asm___9_lld_con_c_df553fb8____RRX = 0x000005a9;
__asm___10_lld_init_c_b391185e____RRX = 0x000005b1;
__asm___13_lld_per_adv_c_77b63e9a____RRX = 0x000005b9;
__asm___10_lld_scan_c_01456b52____RRX = 0x000005c1;
__asm___10_lld_sync_c_8bd34c56____RRX = 0x000005c9;
__asm___10_lld_test_c_54e5e101____RRX = 0x000005d1;
__asm___5_llm_c_52f69136____RRX = 0x000005d9;
__asm___9_llm_adv_c_2ca465c6____RRX = 0x000005e1;
__asm___9_llm_hci_c_20aee1fa____RRX = 0x000005e9;
__asm___10_llm_init_c_c210a516____RRX = 0x000005f1;
__asm___9_llm_iso_c____RRX = 0x000005f9;
__asm___10_llm_scan_c_963e4937____RRX = 0x00000601;
__asm___10_llm_task_c_5d3df270____RRX = 0x00000609;
__asm___5_hci_c_hci_init____RRX = 0x00000611;
__asm___8_hci_fc_c_fcf3c82e____RRX = 0x00000619;
__asm___9_hci_msg_c_b5066cf5____RRX = 0x00000621;
__asm___8_hci_tl_c_9ce329a5____RRX = 0x00000629;
__asm___11_sch_alarm_c_3b4969f2____RRX = 0x00000631;
__asm___9_sch_arb_c_87561c0e____RRX = 0x00000639;
__asm___10_sch_plan_c_c0257ea0____RRX = 0x00000641;
__asm___10_sch_prog_c_de24772c____RRX = 0x00000649;
__asm___11_sch_slice_c_de6dd3b5____RRX = 0x00000651;
__asm___5_aes_c_aes_init____RRX = 0x00000659;
__asm___9_aes_ccm_c_aes_ccm____RRX = 0x00000661;
__asm___10_aes_cmac_c_fe47f34c____RRX = 0x00000669;
__asm___8_aes_k1_c_aes_k1____RRX = 0x00000671;
__asm___8_aes_k2_c_5cc4f012____RRX = 0x00000679;
__asm___8_aes_k3_c_979823b7____RRX = 0x00000681;
__asm___8_aes_k4_c_8a9d130f____RRX = 0x00000689;
__asm___8_aes_s1_c_aes_s1____RRX = 0x00000691;
__asm___9_co_list_c_ca99e89f____RRX = 0x00000699;
__asm___10_co_utils_c_one_bits____RRX = 0x000006a1;
__asm___5_dbg_c_3a87ab48____RRX = 0x000006a9;
__asm___12_dbg_mwsgen_c____RRX = 0x000006b1;
__asm___12_dbg_swdiag_c____RRX = 0x000006b9;
__asm___10_dbg_task_c____RRX = 0x000006c1;
__asm___9_dbg_trc_c____RRX = 0x000006c9;
__asm___13_dbg_trc_mem_c____RRX = 0x000006d1;
__asm___12_dbg_trc_tl_c____RRX = 0x000006d9;
__asm___9_display_c____RRX = 0x000006e1;
__asm___14_display_task_c____RRX = 0x000006e9;
__asm___10_ecc_p256_c_8b173798____RRX = 0x000006f1;
__asm___6_h4tl_c_3a5732fe____RRX = 0x000006f9;
__asm___4_ke_c_ke_init____RRX = 0x00000701;
__asm___10_ke_event_c_87ccd12c____RRX = 0x00000709;
__asm___8_ke_mem_c_f92e48dd____RRX = 0x00000711;
__asm___8_ke_msg_c_d502b297____RRX = 0x00000719;
__asm___10_ke_queue_c_5cab850e____RRX = 0x00000721;
__asm___9_ke_task_c_18478d77____RRX = 0x00000729;
__asm___10_ke_timer_c_3b621a08____RRX = 0x00000731;
__asm___6_nvds_c____RRX = 0x00000739;
__asm___9_rf_simu_c_82de3823____RRX = 0x00000741;
__asm___6_rwip_c_f2b147fa____RRX = 0x00000749;
__asm___13_rwip_driver_c_c12c84cf____RRX = 0x00000751;
__asm___11_co_printf_c_fputc____RRX = 0x00000759;
__asm___11_low_power_c_50b0e29e____RRX = 0x00000761;
__asm___7_crc32_c_36f7c2a5____RRX = 0x00000769;
__asm___5_md5_c_MD5_Init____RRX = 0x00000771;
__asm___11_arch_main_c_uart_api____RRX = 0x00000779;
__asm___12_jump_table_c_6a9c728e____RRX = 0x00000781;
__asm___6_intc_c_189c7b66____RRX = 0x00000789;
__asm___6_uart_c_988f2830____RRX = 0x00000791;
__asm___7_flash_c_1f9d869b____RRX = 0x00000799;
__asm___6_qspi_c_79b82d8e____RRX = 0x000007a1;
__asm___8_system_c_a51710b5____RRX = 0x000007a9;
__asm___6_gpio_c_a47c3f18____RRX = 0x000007b1;
__asm___5_ssp_c_3d1cf05e____RRX = 0x000007b9;
__asm___8_frspim_c_6e8f94f7____RRX = 0x000007c1;
__asm___5_pmu_c_7ed478f1____RRX = 0x000007c9;
__asm___9_apb2spi_c_a124e987____RRX = 0x000007d1;
__asm___6_trng_c_36387bb2____RRX = 0x000007d9;
__asm___6_list_c_a968f7cb____RRX = 0x000007e1;
__asm___7_queue_c_48e2f297____RRX = 0x000007e9;
__asm___7_tasks_c_f31043e3____RRX = 0x000007f1;
__asm___8_timers_c_a20bbafd____RRX = 0x000007f9;
__asm___14_event_groups_c_5f98c4ce____RRX = 0x00000801;
__asm___8_heap_6_c_94e30ff9____RRX = 0x00000809;
__asm___6_port_c_39a90d8d____RRX = 0x00000811;
__asm___20_freertos_interface_c_bc2b671d____RRX = 0x00000819;
Reset_Handler = 0x00000821;
set_handlemode_sp = 0x00000853;
NMI_Handler = 0x00000859;
HardFault_Handler = 0x0000085b;
MemManage_Handler = 0x0000085d;
BusFault_Handler = 0x0000085f;
UsageFault_Handler = 0x00000861;
DebugMon_Handler = 0x00000865;
EXTI0_Handler = 0x0000086b;
EXTI1_Handler = 0x0000086d;
EXTI2_Handler = 0x0000086f;
EXTI3_Handler = 0x00000871;
EXTI4_Handler = 0x00000873;
ext_int_isr = 0x00000875;
i2s_isr = 0x00000879;
pdm_isr = 0x0000087b;
CPU_SR_Save = 0x00000881;
CPU_SR_Restore = 0x00000891;
GLOBAL_INT_STOP = 0x00000897;
GLOBAL_INT_START = 0x0000089b;
rand = 0x000008a1;
srand = 0x000008b5;
__aeabi_memcpy = 0x000008c5;
__aeabi_memcpy4 = 0x000008c5;
__aeabi_memcpy8 = 0x000008c5;
__aeabi_memset = 0x000008e9;
__aeabi_memset4 = 0x000008e9;
__aeabi_memset8 = 0x000008e9;
__aeabi_memclr = 0x000008f7;
__aeabi_memclr4 = 0x000008f7;
__aeabi_memclr8 = 0x000008f7;
_memset$wrapper = 0x000008fb;
memset = 0x000008fb;
memcmp = 0x0000090d;
__scatterload = 0x00000929;
__scatterload_rt2 = 0x00000929;
__decompress = 0x0000094d;
__decompress0 = 0x0000094d;
Add2SelfBigHex256 = 0x00000989;
AddBigHex256 = 0x000009d1;
AddBigHexModP256 = 0x00000a1d;
AddP256 = 0x00000a6d;
AddPdiv2_256 = 0x00000a99;
FPB_CompSet = 0x00000bad;
GF_Jacobian_Point_Addition256 = 0x00000bd5;
GF_Jacobian_Point_Double256 = 0x00000f91;
GF_Point_Jacobian_To_Affine256 = 0x00001109;
MD5_Final = 0x00001135;
MD5_Init = 0x0000120d;
MD5_Update = 0x00001231;
MultiplyBigHexByUint32_256 = 0x000012ad;
MultiplyBigHexModP256 = 0x000012f1;
MultiplyByU32ModP256 = 0x00001431;
NVIC_DisableIRQ = 0x00001461;
NVIC_EnableIRQ = 0x0000147b;
NVIC_SetPriority = 0x00001495;
SubtractBigHex256 = 0x000014d5;
SubtractBigHexMod256 = 0x00001529;
SubtractBigHexUint32_256 = 0x00001611;
SubtractFromSelfBigHex256 = 0x00001667;
SubtractFromSelfBigHexSign256 = 0x000016b9;
SysTick_Handler = 0x00001775;
__scatterload_copy = 0x000017a1;
__scatterload_null = 0x000017af;
__scatterload_zeroinit = 0x000017b1;
aes_alloc = 0x000017bf;
aes_ccm = 0x000017d5;
aes_cmac = 0x00001b05;
aes_cmac_continue = 0x00001b31;
aes_cmac_start = 0x00001c75;
aes_encrypt = 0x00001cad;
aes_init = 0x00001d01;
aes_k1 = 0x00001d29;
aes_k2 = 0x00001da1;
aes_k3 = 0x00001ec5;
aes_k4 = 0x00001f31;
aes_rand = 0x00001fa5;
aes_result_handler = 0x00001fd5;
aes_s1 = 0x0000202d;
aes_shift_left_128 = 0x00002051;
aes_start = 0x00002075;
aes_xor_128 = 0x000020b1;
apb2spi_init = 0x000020c9;
apb2spi_read = 0x000020dd;
apb2spi_write = 0x00002145;
app_boot = 0x000021a5;
app_boot_detect_storage_type = 0x00002241;
app_boot_get_buffer = 0x00002261;
app_boot_get_storage_type = 0x00002269;
app_boot_host_comm = 0x00002279;
app_boot_host_comm_loop = 0x000022ed;
app_boot_init = 0x00002351;
app_boot_load = 0x00002399;
app_boot_load_data = 0x0000240d;
app_boot_process_cmd = 0x00002439;
app_boot_save_data = 0x000026c1;
app_boot_send_rsp = 0x000026e1;
app_boot_serial_gets = 0x000026f1;
app_boot_set_storage_type = 0x00002735;
bigHexInversion256 = 0x00002749;
ble_util_buf_acl_tx_alloc = 0x00002965;
ble_util_buf_acl_tx_elt_get = 0x0000297d;
ble_util_buf_acl_tx_free = 0x000029a5;
ble_util_buf_adv_tx_alloc = 0x000029d1;
ble_util_buf_adv_tx_free = 0x000029e5;
ble_util_buf_advexthdr_alloc = 0x00002a11;
ble_util_buf_advexthdr_free = 0x00002a75;
ble_util_buf_init = 0x00002ab9;
ble_util_buf_init_env = 0x00002c25;
ble_util_buf_llcp_tx_alloc = 0x00002c35;
ble_util_buf_llcp_tx_free = 0x00002c41;
ble_util_buf_rx_alloc = 0x00002c65;
ble_util_buf_rx_free = 0x00002c8d;
co_bdaddr_compare = 0x000034a7;
co_ble_pkt_dur_in_us = 0x000034c3;
co_list_extract = 0x0000358d;
co_list_extract_after = 0x000035bd;
co_list_extract_sublist = 0x000035d5;
co_list_find = 0x000035e9;
co_list_init = 0x000035ff;
co_list_insert_after = 0x00003607;
co_list_insert_before = 0x00003631;
co_list_merge = 0x00003659;
co_list_pool_init = 0x00003671;
co_list_pop_front = 0x000036d5;
co_list_push_back = 0x000036e9;
co_list_push_back_sublist = 0x000036fd;
co_list_push_front = 0x00003711;
co_list_size = 0x0000371d;
co_nb_good_le_channels = 0x00003731;
co_printf = 0x00003759;
co_sprintf = 0x0000376d;
co_util_pack = 0x00003781;
co_util_unpack = 0x00003963;
crc32 = 0x00003b21;
dbg_platform_reset_complete = 0x00003bd5;
divideByTwo256 = 0x00003c09;
dl_upd_proc_start = 0x00003c49;
eTaskConfirmSleepModeStatus = 0x00003cbd;
ecc_abort_key256_generation = 0x00003ced;
ecc_gen_new_public_key = 0x00003d39;
ecc_gen_new_secret_key = 0x00003d55;
ecc_generate_key256 = 0x00003e45;
ecc_get_debug_Keys = 0x000040f1;
ecc_init = 0x00004119;
em_ble_generate_base_address_table = 0x000046a5;
flash_enter_deep_sleep = 0x00004755;
flash_exit_deep_sleep = 0x000047e9;
flash_read = 0x00004809;
flash_read_status = 0x00004871;
flash_write_status = 0x00004951;
fputc = 0x0000499d;
freertos_baseband_restore_done = 0x000049ed;
frspim_init = 0x00004a8d;
get_crc_table = 0x00004b51;
get_stack_limit = 0x00004b59;
get_stack_usage = 0x00004b65;
gpio_get_pin_value = 0x00004b79;
gpio_set_dir = 0x00004bb5;
gpio_set_pin_value = 0x00004c09;
h4tl_init = 0x00004d2d;
h4tl_start = 0x00004f05;
h4tl_stop = 0x00004f25;
h4tl_write = 0x0000501d;
hci_acl_data_handler = 0x00005071;
hci_acl_tx_data_alloc = 0x00005111;
hci_acl_tx_data_received = 0x0000517d;
hci_basic_cmd_send_2_controller = 0x000051d1;
hci_ble_conhdl_register = 0x000052a9;
hci_ble_conhdl_unregister = 0x000052b9;
hci_cmd_get_max_param_size = 0x0000539b;
hci_cmd_received = 0x000053ad;
hci_command_handler = 0x000055bd;
hci_disconnect_cmd_handler = 0x000055f1;
hci_evt_mask_set = 0x00005705;
hci_fc_acl_buf_size_set = 0x00005755;
hci_fc_acl_en = 0x00005769;
hci_fc_acl_packet_sent = 0x000057a1;
hci_fc_check_host_available_nb_acl_packets = 0x000057b5;
hci_fc_host_nb_acl_pkts_complete = 0x000057d9;
hci_fc_init = 0x000057f1;
hci_init = 0x000058f1;
hci_le_add_dev_to_per_adv_list_cmd_handler = 0x00005939;
hci_le_clear_adv_sets_cmd_handler = 0x00005c79;
hci_le_clear_per_adv_list_cmd_handler = 0x00005d05;
hci_le_con_upd_cmd_handler = 0x00005e09;
hci_le_create_con_cancel_cmd_handler = 0x00005f29;
hci_le_create_con_cmd_handler = 0x00005fa1;
hci_le_ext_create_con_cmd_handler = 0x0000660d;
hci_le_ltk_req_neg_reply_cmd_handler = 0x00006b3d;
hci_le_ltk_req_reply_cmd_handler = 0x00006bcd;
hci_le_per_adv_create_sync_cancel_cmd_handler = 0x00006c45;
hci_le_per_adv_create_sync_cmd_handler = 0x00006cc9;
hci_le_per_adv_term_sync_cmd_handler = 0x00006d95;
hci_le_rd_adv_ch_tx_pw_cmd_handler = 0x00006e39;
hci_le_rd_chnl_map_cmd_handler = 0x00006ebd;
hci_le_rd_max_adv_data_len_cmd_handler = 0x00006fe1;
hci_le_rd_nb_supp_adv_sets_cmd_handler = 0x00007061;
hci_le_rd_per_adv_list_size_cmd_handler = 0x00007105;
hci_le_rd_phy_cmd_handler = 0x0000713d;
hci_le_rd_rem_feats_cmd_handler = 0x0000718d;
hci_le_rem_con_param_req_neg_reply_cmd_handler = 0x00007349;
hci_le_rem_con_param_req_reply_cmd_handler = 0x000073a9;
hci_le_rmv_adv_set_cmd_handler = 0x00007435;
hci_le_rmv_dev_from_per_adv_list_cmd_handler = 0x000074a9;
hci_le_set_adv_data_cmd_handler = 0x00007691;
hci_le_set_adv_en_cmd_handler = 0x00007769;
hci_le_set_adv_param_cmd_handler = 0x00007979;
hci_le_set_adv_set_rand_addr_cmd_handler = 0x00007ad9;
hci_le_set_data_len_cmd_handler = 0x00007b59;
hci_le_set_ext_adv_data_cmd_handler = 0x00007cd9;
hci_le_set_ext_adv_en_cmd_handler = 0x00007edd;
hci_le_set_ext_adv_param_cmd_handler = 0x000083cd;
hci_le_set_ext_scan_en_cmd_handler = 0x000085c5;
hci_le_set_ext_scan_param_cmd_handler = 0x00008865;
hci_le_set_ext_scan_rsp_data_cmd_handler = 0x00008a91;
hci_le_set_per_adv_data_cmd_handler = 0x00008c8d;
hci_le_set_per_adv_en_cmd_handler = 0x00008e19;
hci_le_set_per_adv_param_cmd_handler = 0x00008fc5;
hci_le_set_phy_cmd_handler = 0x000090c5;
hci_le_set_scan_en_cmd_handler = 0x000092a1;
hci_le_set_scan_param_cmd_handler = 0x000094b9;
hci_le_set_scan_rsp_data_cmd_handler = 0x000095f9;
hci_le_start_enc_cmd_handler = 0x000096d1;
hci_look_for_cmd_desc = 0x000098a9;
hci_look_for_evt_desc = 0x000098f9;
hci_look_for_le_evt_desc = 0x00009921;
hci_rd_auth_payl_to_cmd_handler = 0x00009985;
hci_rd_rem_ver_info_cmd_handler = 0x00009a99;
hci_rd_rssi_cmd_handler = 0x00009b29;
hci_rd_tx_pwr_lvl_cmd_handler = 0x00009b95;
hci_send_2_controller = 0x00009c25;
hci_send_2_host = 0x00009c9d;
hci_tl_init = 0x00009dc9;
hci_tl_send = 0x00009df5;
hci_vs_set_pref_slave_latency_cmd_handler = 0x0000a009;
hci_wr_auth_payl_to_cmd_handler = 0x0000a04d;
intc_init = 0x0000a121;
ke_check_malloc = 0x0000a1d5;
ke_event_callback_set = 0x0000a259;
ke_event_clear = 0x0000a271;
ke_event_flush = 0x0000a299;
ke_event_get = 0x0000a2a5;
ke_event_get_all = 0x0000a2cd;
ke_event_init = 0x0000a2d9;
ble_stack_schedule = 0x0000a2e5;
ke_event_set = 0x0000a329;
ke_flush = 0x0000a369;
ke_free = 0x0000a3a5;
ke_get_mem_usage = 0x0000a475;
ke_init = 0x0000a485;
ke_is_free = 0x0000a4b9;
ke_malloc = 0x0000a4cd;
ke_mem_init = 0x0000a5a1;
ke_mem_is_empty = 0x0000a5f1;
ke_msg_alloc = 0x0000a625;
ke_msg_dest_id_get = 0x0000a65b;
ke_msg_discard = 0x0000a661;
ke_msg_forward = 0x0000a665;
ke_msg_forward_new_id = 0x0000a671;
ke_msg_free = 0x0000a683;
ke_msg_in_queue = 0x0000a687;
ke_msg_save = 0x0000a693;
ke_msg_send = 0x0000a699;
ke_msg_send_basic = 0x0000a6c5;
ke_msg_src_id_get = 0x0000a6d5;
ke_queue_extract = 0x0000a6db;
ke_queue_insert = 0x0000a719;
ke_sleep_check = 0x0000a749;
ke_state_get = 0x0000a759;
ke_state_set = 0x0000a789;
ke_task_check = 0x0000a7c9;
ke_task_create = 0x0000a7f5;
ke_task_init = 0x0000a8d1;
ke_task_msg_flush = 0x0000a8e9;
ke_time = 0x0000aa09;
ke_timer_active = 0x0000aa45;
ke_timer_adjust_all = 0x0000aa6d;
ke_timer_clear = 0x0000aa85;
ke_timer_init = 0x0000aaed;
ke_timer_set = 0x0000ab4d;
ll_channel_map_ind_handler = 0x0000abf9;
ll_connection_param_req_handler = 0x0000ac89;
ll_connection_param_rsp_handler = 0x0000ad61;
ll_connection_update_ind_handler = 0x0000adf1;
ll_enc_req_handler = 0x0000af09;
ll_enc_rsp_handler = 0x0000afc5;
ll_feature_req_handler = 0x0000b031;
ll_feature_rsp_handler = 0x0000b0a1;
ll_length_req_handler = 0x0000b0f1;
ll_length_rsp_handler = 0x0000b145;
ll_min_used_channels_ind_handler = 0x0000b1c1;
ll_pause_enc_req_handler = 0x0000b245;
ll_pause_enc_rsp_handler = 0x0000b2b1;
ll_phy_req_handler = 0x0000b309;
ll_phy_rsp_handler = 0x0000b3b9;
ll_phy_update_ind_handler = 0x0000b431;
ll_ping_req_handler = 0x0000b4bf;
ll_ping_rsp_handler = 0x0000b4d3;
ll_slave_feature_req_handler = 0x0000b52f;
ll_start_enc_req_handler = 0x0000b535;
ll_start_enc_rsp_handler = 0x0000b585;
ll_terminate_ind_handler = 0x0000b5d5;
ll_version_ind_handler = 0x0000b621;
llc_auth_payl_nearly_to_handler = 0x0000b6b1;
llc_auth_payl_real_to_handler = 0x0000b715;
llc_cleanup = 0x0000b7a1;
llc_cmd_cmp_send = 0x0000b809;
llc_cmd_stat_send = 0x0000b829;
llc_con_move_cbk = 0x0000b845;
llc_disconnect = 0x0000b97d;
llc_encrypt_ind_handler = 0x0000ba8d;
llc_init = 0x0000bdf1;
llc_init_term_proc = 0x0000be4d;
llc_le_ping_restart = 0x0000bf09;
llc_le_ping_set = 0x0000bf45;
llc_ll_reject_ind_pdu_send = 0x0000bfe9;
llc_llcp_send = 0x0000c08d;
llc_llcp_state_set = 0x0000c10d;
llc_llcp_tx_check = 0x0000c1c1;
llc_op_ch_map_upd_ind_handler = 0x0000cee1;
llc_op_con_upd_ind_handler = 0x0000cf45;
llc_op_disconnect_ind_handler = 0x0000cfd9;
llc_op_dl_upd_ind_handler = 0x0000d049;
llc_op_encrypt_ind_handler = 0x0000d0b9;
llc_op_feats_exch_ind_handler = 0x0000d11d;
llc_op_le_ping_ind_handler = 0x0000d185;
llc_op_phy_upd_ind_handler = 0x0000d1d9;
llc_op_ver_exch_ind_handler = 0x0000d261;
llc_proc_collision_check = 0x0000d3f5;
llc_proc_err_ind = 0x0000d41d;
llc_proc_get = 0x0000d44d;
llc_proc_id_get = 0x0000d465;
llc_proc_reg = 0x0000d481;
llc_proc_state_get = 0x0000d4b5;
llc_proc_state_set = 0x0000d4b9;
llc_proc_timer_pause_set = 0x0000d4bd;
llc_proc_timer_set = 0x0000d519;
llc_proc_unreg = 0x0000d571;
llc_start = 0x0000dde9;
llc_stop = 0x0000df85;
llc_stopped_ind_handler = 0x0000dfb9;
lld_aa_gen = 0x0000e091;
lld_acl_rx_ind_handler = 0x0000e10d;
lld_acl_tx_cfm_handler = 0x0000e191;
lld_adv_adv_data_update = 0x0000e265;
lld_adv_duration_update = 0x0000e529;
lld_adv_end_ind_handler = 0x0000e5fd;
lld_adv_init = 0x0000f995;
lld_adv_rand_addr_update = 0x0000fd1d;
lld_adv_rep_ind_handler = 0x0000fd89;
lld_adv_restart = 0x00010235;
lld_adv_scan_rsp_data_update = 0x00010391;
lld_adv_start = 0x000103d5;
lld_adv_stop = 0x00010ac1;
lld_adv_sync_info_update = 0x00010cdd;
lld_calc_aux_rx = 0x00010d0d;
lld_ch_assess_data_get = 0x00010e29;
lld_ch_map_upd_cfm_handler = 0x00010e31;
lld_channel_assess = 0x00010e69;
lld_con_activity_act_offset_compute = 0x00010e99;
lld_con_activity_offset_compute = 0x00010f4d;
lld_con_ch_map_update = 0x00010fdd;
lld_con_current_tx_power_get = 0x00011105;
lld_con_data_flow_set = 0x0001115d;
lld_con_data_len_update = 0x00011211;
lld_con_data_tx = 0x00011269;
lld_con_enc_key_load = 0x00011319;
lld_con_estab_ind_handler = 0x000113d5;
lld_con_event_counter_get = 0x000113fd;
lld_con_evt_canceled_cbk = 0x00011421;
lld_con_evt_start_cbk = 0x00011461;
lld_con_evt_time_update = 0x00011651;
lld_con_init = 0x000118b9;
lld_con_llcp_tx = 0x000118f5;
lld_con_max_lat_calc = 0x00011999;
lld_con_offset_get = 0x000119f9;
lld_con_offset_upd_ind_handler = 0x00011a3d;
lld_con_param_upd_cfm_handler = 0x00011a81;
lld_con_param_update = 0x00011aed;
lld_con_phys_update = 0x00011b75;
lld_con_pref_slave_latency_set = 0x00011bdd;
lld_con_rssi_get = 0x00011c0d;
lld_con_rx_enc = 0x00011ebd;
lld_con_sched = 0x00011f05;
lld_con_start = 0x00012239;
lld_con_stop = 0x000125f5;
lld_con_tx_enc = 0x00012761;
lld_con_tx_len_update_for_intv = 0x000127f5;
lld_con_tx_len_update_for_rate = 0x00012839;
lld_disc_ind_handler = 0x00012c15;
lld_init = 0x00012c51;
lld_init_connect_req_pack = 0x00012d37;
lld_init_end_ind_handler = 0x00012e65;
lld_init_init = 0x0001342d;
lld_init_start = 0x00013c31;
lld_init_stop = 0x0001431d;
lld_llcp_rx_ind_handler = 0x000143cd;
lld_llcp_tx_cfm_handler = 0x00014531;
lld_per_adv_ch_map_update = 0x00014575;
lld_per_adv_data_update = 0x000147a5;
lld_per_adv_end_ind_handler = 0x000147e9;
lld_per_adv_init = 0x00014ed9;
lld_per_adv_init_info_get = 0x00014f15;
lld_per_adv_list_add = 0x00014f5d;
lld_per_adv_list_rem = 0x00015005;
lld_per_adv_rep_ind_handler = 0x0001507d;
lld_per_adv_rx_end_ind_handler = 0x00015365;
lld_per_adv_start = 0x00015541;
lld_per_adv_stop = 0x000157b1;
lld_per_adv_sync_info_get = 0x00015841;
lld_phy_upd_cfm_handler = 0x0001589d;
lld_ral_search = 0x000158d5;
lld_read_clock = 0x00015935;
lld_res_list_add = 0x00015941;
lld_res_list_clear = 0x00015a49;
lld_res_list_is_empty = 0x00015a7d;
lld_res_list_local_rpa_get = 0x00015ab5;
lld_res_list_peer_check = 0x00015af9;
lld_res_list_peer_rpa_get = 0x00015b31;
lld_res_list_peer_update = 0x00015b75;
lld_res_list_priv_mode_update = 0x00015bb1;
lld_res_list_rem = 0x00015bf9;
lld_rpa_renew = 0x00015c31;
lld_rxdesc_check = 0x00015d7d;
lld_rxdesc_free = 0x00015db5;
lld_scan_create_sync = 0x00015e15;
lld_scan_create_sync_cancel = 0x00015e55;
lld_scan_end_ind_handler = 0x00015f35;
lld_scan_init = 0x00016255;
lld_scan_params_update = 0x00016295;
lld_scan_req_ind_handler = 0x00016c05;
lld_scan_restart = 0x00016c51;
lld_scan_start = 0x00016fe9;
lld_scan_stop = 0x000174b1;
lld_sync_ch_map_update = 0x00017669;
lld_sync_init = 0x000178b9;
lld_sync_start = 0x00017ead;
lld_sync_start_req_handler = 0x000180a5;
lld_sync_stop = 0x000181a1;
lld_test_end_ind_handler = 0x0001822d;
lld_test_init = 0x00018395;
lld_test_start = 0x000183a9;
lld_test_stop = 0x00018611;
lld_white_list_add = 0x000186c1;
lld_white_list_rem = 0x00018759;
llm_activity_free_get = 0x000187d1;
llm_activity_free_set = 0x00018835;
llm_adv_hdl_to_id = 0x000188b5;
llm_ch_map_update = 0x00018ae1;
llm_ch_map_update_ind_handler = 0x00018c31;
llm_cmd_cmp_send = 0x00018c9d;
llm_cmd_stat_send = 0x00018cb9;
llm_dev_list_empty_entry = 0x00018cd5;
llm_dev_list_search = 0x00018cf9;
llm_hci_command_handler = 0x00018d99;
llm_init = 0x00018dcd;
llm_init_act_info_buf = 0x00018efd;
llm_is_dev_connected = 0x00018f09;
llm_is_dev_synced = 0x00018f79;
llm_le_evt_mask_check = 0x00019099;
llm_le_features_get = 0x000190b9;
llm_link_disc = 0x000190c1;
llm_master_ch_map_get = 0x00019135;
llm_plan_elt_get = 0x00019189;
llm_rx_path_comp_get = 0x00019215;
llm_scan_sync_acad_attach = 0x00019221;
llm_tx_path_comp_get = 0x00019265;
low_power_enter_sleep = 0x00019271;
low_power_restore = 0x0001927d;
low_power_save = 0x0001937d;
main = 0x000193fd;
notEqual256 = 0x00019545;
pcTaskGetName = 0x00019565;
pcTimerGetName = 0x00019575;
phy_upd_proc_start = 0x00019579;
platform_reset = 0x000195d1;
pmu_calibration_start = 0x000195f9;
pmu_calibration_stop = 0x00019679;
pmu_disable_isr = 0x000196a9;
pmu_disable_isr2 = 0x000196c9;
pmu_enable_isr = 0x000196e9;
pmu_enable_isr2 = 0x00019709;
pmu_first_power_on = 0x00019729;
pmu_get_isr_state = 0x00019751;
pmu_get_rc_clk = 0x00019761;
pmu_gpio_set_dir = 0x000197a9;
pmu_init = 0x000197e1;
pmu_isr = 0x000197fd;
pmu_port_set_mux = 0x0001993d;
pmu_port_wakeup_func_clear = 0x00019975;
pmu_port_wakeup_func_set = 0x00019995;
pmu_set_on_off_mode = 0x00019a4d;
print = 0x00019a81;
pvPortMalloc = 0x0001a2d9;
pvTaskIncrementMutexHeldCount = 0x0001a305;
pvTimerGetTimerID = 0x0001a31d;
pxPortInitialiseStack = 0x0001a331;
qspi_cfg_set_baudrate = 0x0001a355;
qspi_flash_enable_quad = 0x0001a369;
qspi_flash_init = 0x0001a3dd;
qspi_flash_init_controller = 0x0001a401;
qspi_stig_cmd = 0x0001a575;
rf_em_init = 0x0001a855;
rf_init_api = 0x0001a879;
rf_init_controller = 0x0001a8b1;
rf_init_rom = 0x0001a905;
rwble_init = 0x0001a97d;
rwble_isr = 0x0001a9bd;
rwble_sleep_enter = 0x0001aa1d;
rwble_sleep_wakeup_end = 0x0001aa35;
rwip_active_check = 0x0001aa45;
rwip_aes_encrypt = 0x0001aa49;
rwip_driver_init = 0x0001aaf1;
rwip_eif_get = 0x0001abb5;
rwip_init = 0x0001abc5;
rwip_isr = 0x0001ac5d;
rwip_lpcycles_2_hus = 0x0001ace9;
rwip_prevent_sleep_clear = 0x0001ad11;
rwip_prevent_sleep_set = 0x0001ad31;
rwip_reset = 0x0001ad51;
rwip_schedule = 0x0001adb1;
rwip_sleep = 0x0001adc5;
rwip_sleep_div_64 = 0x0001adfd;
rwip_sleep_time_calc = 0x0001ae35;
rwip_slot_2_lpcycles = 0x0001aef1;
rwip_sw_int_req = 0x0001af1d;
rwip_time_get = 0x0001af37;
rwip_timer_10ms_set = 0x0001af7d;
rwip_timer_hs_set = 0x0001afdd;
rwip_timer_hus_set = 0x0001b025;
rwip_us_2_lpcycles = 0x0001b059;
sch_alarm_clear = 0x0001b181;
sch_alarm_init = 0x0001b1c5;
sch_alarm_set = 0x0001b22d;
sch_alarm_timer_isr = 0x0001b2bd;
sch_arb_elt_cancel = 0x0001b3c5;
sch_arb_event_start_isr = 0x0001b49d;
sch_arb_init = 0x0001b53d;
sch_arb_insert = 0x0001b559;
sch_arb_remove = 0x0001b7b5;
sch_arb_sw_isr = 0x0001b815;
sch_plan_chk = 0x0001b839;
sch_plan_init = 0x0001b845;
sch_plan_position_range_compute = 0x0001bb19;
sch_plan_rem = 0x0001bc3d;
sch_plan_req = 0x0001bc49;
sch_plan_set = 0x0001bc7d;
sch_prog_end_isr = 0x0001bce1;
sch_prog_init = 0x0001bd6d;
sch_prog_push = 0x0001bda1;
sch_prog_rx_isr = 0x0001bed1;
sch_prog_skip_isr = 0x0001bef1;
sch_prog_tx_isr = 0x0001bf85;
sch_slice_bg_add = 0x0001bfa5;
sch_slice_bg_remove = 0x0001bfb9;
sch_slice_compute = 0x0001bfcd;
sch_slice_fg_add = 0x0001c059;
sch_slice_fg_remove = 0x0001c079;
sch_slice_init = 0x0001c0ad;
sch_slice_per_add = 0x0001c0d5;
sch_slice_per_remove = 0x0001c0fd;
specialModP256 = 0x0001c121;
ssp_clear_rx_fifo = 0x0001c1e5;
ssp_disable = 0x0001c1f7;
ssp_enable = 0x0001c201;
ssp_get_data = 0x0001c20b;
ssp_init = 0x0001c22d;
ssp_put_byte = 0x0001c295;
ssp_put_data = 0x0001c29f;
ssp_reconfigure = 0x0001c2b5;
ssp_wait_busy_bit = 0x0001c301;
ssp_write_then_read = 0x0001c309;
system_get_pclk = 0x0001c371;
system_get_pclk_config = 0x0001c385;
system_init = 0x0001c391;
system_set_cache_config = 0x0001c3cd;
system_set_pclk = 0x0001c3dd;
system_set_port_mux = 0x0001c3fd;
system_set_port_pull = 0x0001c417;
trng_init = 0x0001c4d5;
trng_read_rand_num = 0x0001c4d9;
uart0_isr = 0x0001c531;
uart0_read_for_hci = 0x0001c591;
uart0_write_for_hci = 0x0001c5a9;
uart1_isr = 0x0001c5d1;
uart1_read_for_hci = 0x0001c631;
uart1_write_for_hci = 0x0001c649;
uart_finish_transfers = 0x0001c671;
uart_flow_off = 0x0001c679;
uart_flow_on = 0x0001c67d;
uart_flush_rxfifo_noint = 0x0001c67f;
uart_get_data_nodelay_noint = 0x0001c691;
uart_get_data_noint = 0x0001c6b5;
uart_init = 0x0001c6d9;
uart_put_data_noint = 0x0001c779;
uart_putc_noint = 0x0001c79d;
uart_putc_noint_no_wait = 0x0001c7a7;
uart_read = 0x0001c7ab;
uart_reset_register = 0x0001c7cd;
uart_set_fifo = 0x0001c7e1;
uart_write = 0x0001c7e5;
ulTaskNotifyTake = 0x0001c80d;
uxListRemove = 0x0001c875;
uxQueueMessagesWaiting = 0x0001c89b;
uxQueueMessagesWaitingFromISR = 0x0001c8ad;
uxQueueSpacesAvailable = 0x0001c8b1;
uxTaskGetNumberOfTasks = 0x0001c8c9;
uxTaskPriorityGet = 0x0001c8d5;
uxTaskPriorityGetFromISR = 0x0001c8f1;
uxTaskResetEventItemValue = 0x0001c915;
vEventGroupClearBitsCallback = 0x0001c92d;
vEventGroupDelete = 0x0001c931;
vEventGroupSetBitsCallback = 0x0001c95d;
vListInitialise = 0x0001c961;
vListInitialiseItem = 0x0001c977;
vListInsert = 0x0001c97d;
vListInsertEnd = 0x0001c9ad;
vPortEndScheduler = 0x0001c9c5;
vPortEnterCritical = 0x0001c9c9;
vPortExitCritical = 0x0001c9e5;
vPortFree = 0x0001c9f9;
vPortSetupTimerInterrupt = 0x0001ca15;
vPortSuppressTicksAndSleep = 0x0001ca69;
vQueueDelete = 0x0001cb81;
vQueueWaitForMessageRestricted = 0x0001cb85;
vTaskDelay = 0x0001cbc9;
vTaskDelayUntil = 0x0001cbf9;
vTaskDelete = 0x0001cc4d;
vTaskEndScheduler = 0x0001cce5;
vTaskInternalSetTimeOutState = 0x0001cd01;
vTaskMissedYield = 0x0001cd11;
vTaskNotifyGiveFromISR = 0x0001cd1d;
vTaskPlaceOnEventList = 0x0001cda9;
vTaskPlaceOnEventListRestricted = 0x0001cdc9;
vTaskPlaceOnUnorderedEventList = 0x0001cdf1;
vTaskPriorityDisinheritAfterTimeout = 0x0001ce1d;
vTaskPrioritySet = 0x0001ce9d;
vTaskRemoveFromUnorderedEventList = 0x0001cf51;
vTaskResume = 0x0001cf9d;
vTaskSetTimeOutState = 0x0001d00d;
vTaskStartScheduler = 0x0001d02d;
vTaskStepTick = 0x0001d089;
vTaskSuspend = 0x0001d099;
vTaskSuspendAll = 0x0001d141;
vTaskSwitchContext = 0x0001d151;
vTimerSetTimerID = 0x0001d195;
xEventGroupClearBits = 0x0001d1a9;
xEventGroupCreate = 0x0001d1c3;
xEventGroupGetBitsFromISR = 0x0001d1dd;
xEventGroupSetBits = 0x0001d1f7;
xEventGroupSync = 0x0001d25d;
xEventGroupWaitBits = 0x0001d2e9;
xPortStartScheduler = 0x0001d399;
xQueueCreateCountingSemaphore = 0x0001d3c9;
xQueueCreateMutex = 0x0001d3dd;
xQueueGenericCreate = 0x0001d401;
xQueueGenericReset = 0x0001d439;
xQueueGenericSend = 0x0001d4ad;
xQueueGenericSendFromISR = 0x0001d5a5;
xQueueGiveFromISR = 0x0001d60b;
xQueueGiveMutexRecursive = 0x0001d661;
xQueueIsQueueEmptyFromISR = 0x0001d68b;
xQueueIsQueueFullFromISR = 0x0001d697;
xQueuePeek = 0x0001d6a9;
xQueuePeekFromISR = 0x0001d791;
xQueueReceive = 0x0001d7bd;
xQueueReceiveFromISR = 0x0001d8ad;
xQueueSemaphoreTake = 0x0001d911;
xQueueTakeMutexRecursive = 0x0001da39;
xTaskCheckForTimeOut = 0x0001da69;
xTaskCreate = 0x0001dab9;
xTaskGenericNotify = 0x0001db11;
xTaskGenericNotifyFromISR = 0x0001dbb9;
xTaskGetCurrentTaskHandle = 0x0001dc75;
xTaskGetSchedulerState = 0x0001dc81;
xTaskGetTickCount = 0x0001dc9d;
xTaskGetTickCountFromISR = 0x0001dca9;
xTaskIncrementTick = 0x0001dcb5;
xTaskNotifyStateClear = 0x0001dd69;
xTaskNotifyWait = 0x0001dd9d;
xTaskPriorityDisinherit = 0x0001de29;
xTaskPriorityInherit = 0x0001de99;
xTaskRemoveFromEventList = 0x0001df2d;
xTaskResumeAll = 0x0001df99;
xTaskResumeFromISR = 0x0001e04d;
xTimerCreate = 0x0001e0c1;
xTimerCreateTimerTask = 0x0001e0f9;
xTimerGenericCommand = 0x0001e135;
xTimerGetExpiryTime = 0x0001e17d;
xTimerGetPeriod = 0x0001e181;
xTimerGetTimerDaemonTaskHandle = 0x0001e185;
xTimerIsTimerActive = 0x0001e191;
app_boot_conn_success = 0x0001e1ac;
app_boot_read_en_magic = 0x0001e1ae;
app_boot_conn_req = 0x0001e1b3;
app_boot_conn_ack = 0x0001e1bb;
TASK_DESC_LLC = 0x0001e4b4;
LLM_AA_CT2 = 0x0001e4c0;
LLM_AA_CT1 = 0x0001e4c2;
connect_req_dur_tab = 0x0001e4c8;
byte_tx_time = 0x0001e4d0;
fixed_tx_time = 0x0001e4d8;
lld_init_max_aux_dur_tab = 0x0001e4e0;
lld_scan_map_legacy_pdu_to_evt_type = 0x0001e4e8;
lld_scan_max_aux_dur_tab = 0x0001e4f0;
lld_sync_max_aux_dur_tab = 0x0001e4f8;
TASK_DESC_LLM = 0x0001e844;
hci_cmd_desc_tab_lk_ctrl = 0x0001e860;
hci_cmd_desc_tab_ctrl_bb = 0x0001e878;
hci_cmd_desc_tab_info_par = 0x0001e8e4;
hci_cmd_desc_tab_stat_par = 0x0001e914;
hci_cmd_desc_tab_le = 0x0001e920;
hci_cmd_desc_tab_vs = 0x0001ecbc;
hci_cmd_desc_root_tab = 0x0001ece0;
hci_evt_desc_tab = 0x0001ed10;
hci_evt_le_desc_tab = 0x0001ed58;
aes_cmac_zero = 0x0001ee00;
aes_k2_salt = 0x0001ee10;
aes_k3_id64 = 0x0001ee20;
aes_k3_salt = 0x0001ee25;
aes_k4_id6 = 0x0001ee35;
aes_k4_salt = 0x0001ee39;
one_bits = 0x0001ee49;
co_sca2ppm = 0x0001ee5a;
co_null_bdaddr = 0x0001ee6a;
co_default_bdaddr = 0x0001ee70;
co_rate_to_phy = 0x0001ee76;
co_phy_to_rate = 0x0001ee7b;
co_phy_mask_to_value = 0x0001ee7f;
co_phy_value_to_mask = 0x0001ee84;
co_rate_to_phy_mask = 0x0001ee88;
BasePoint_x_256 = 0x0001ee8c;
BasePoint_y_256 = 0x0001eeac;
maxSecretKey_256 = 0x0001eecc;
DebugE256PublicKey_x = 0x0001eeec;
DebugE256PublicKey_y = 0x0001ef0c;
DebugE256SecretKey = 0x0001ef2c;
coef_B = 0x0001ef4c;
bigHexP256 = 0x0001ef78;
veryBigHexP256 = 0x0001efa4;
ecc_Jacobian_InfinityPoint256 = 0x0001eff4;
ECC_4Win_Look_up_table = 0x0001f078;
rwip_priority = 0x0001f849;
uart_api = 0x0001fc60;
uart_baud_map = 0x0001fc70;
sector_erase_cmd = 0x0001fc88;
read_id_cmd = 0x0001fc8c;
read_status_cmd = 0x0001fc90;
read_status_h_cmd = 0x0001fc94;
write_enable_cmd = 0x0001fc98;
write_status_cmd = 0x0001fc9c;
write_disable_cmd = 0x0001fca0;
deep_sleep_cmd = 0x0001fca4;
wakeup_cmd = 0x0001fca8;
write_volatile_enable_cmd = 0x0001fcac;
block_erase_cmd = 0x0001fcb0;
write_cmd = 0x0001fcb4;
read_cmd = 0x0001fcb8;
system_clk_map = 0x0001fcbc;
system_regs = 0x0001fcc0;
frspim_reg = 0x0001fcc4;
__jump_table = 0x20000080;
app_storage_base_addr_index = 0x200000dc;
app_boot_param = 0x200000dd;
aa_gen = 0x200000e4;
lld_exp_sync_pos_tab = 0x200000ec;
hci_ext_host = 0x20000105;
sch_slice_params = 0x20000128;
ecc_env = 0x20000130;
ke_task_env = 0x20000138;
rwip_param = 0x2000013c;
__stdout = 0x20000144;
__stdin = 0x20000148;
error = 0x2000014c;
critical_sec_cnt = 0x20000150;
unloaded_area = 0x20000154;
rf_init = 0x20000158;
gapc_get_conidx = 0x2000015c;
appm_init = 0x20000160;
rwble_hl_init = 0x20000164;
svc_exception_handler = 0x20000168;
low_power_save_entry = 0x2000016c;
low_power_restore_entry = 0x20000170;
user_entry_before_sleep = 0x20000174;
user_entry_after_sleep = 0x20000178;
rtos_entry = 0x2000017c;
ke_task_handler_get_user = 0x20000180;
em_ble_base_address_table_0 = 0x20000184;
em_ble_base_address_table_1 = 0x20000186;
em_ble_base_address_table_2 = 0x20000188;
em_ble_base_address_table_3 = 0x2000018a;
em_ble_base_address_table_4 = 0x2000018c;
em_ble_base_address_table_5 = 0x2000018e;
em_ble_base_address_table_6 = 0x20000190;
em_ble_base_address_table_7 = 0x20000192;
em_ble_base_address_table_8 = 0x20000194;
em_ble_base_address_table_9 = 0x20000196;
em_ble_base_address_table_10 = 0x20000198;
qspi_ctrl = 0x2000019c;
pmu_calibration_isr_count = 0x200001ac;
pxCurrentTCB = 0x200001b0;
app_user_rtos_wait_wakeup_end = 0x20000210;
llc_env = 0x20000254;
lld_env = 0x200002c4;
lld_con_env = 0x2000040c;
lld_sync_env = 0x200004bc;
llm_env = 0x20000514;
hci_env = 0x200006e4;
aes_env = 0x20000918;
ke_env = 0x20000944;
rwip_rf = 0x200009b4;
rwip_env = 0x200009e8;
low_power_store_addr = 0x20000a00;
low_power_restore_remap = 0x20000a34;
__initial_sp = 0x40004000;
