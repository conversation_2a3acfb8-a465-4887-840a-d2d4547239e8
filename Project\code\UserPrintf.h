/*
 * @Author: eric
 * @Date: 2023-04-07 09:20:26
 * @LastEditTime: 2023-06-07 16:44:49
 * @Description:  This file is used to define user printf function.
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */
#ifndef _USER_PRINTF_H
#define _USER_PRINTF_H

typedef char * user_va_list;

#define _INTSIZEOF(n)    ((sizeof(n)+sizeof(int)-1)&~(sizeof(int) - 1) )
#define user_va_start(ap,fmt) ( ap = (user_va_list)&fmt + _INTSIZEOF(fmt) )
#define user_va_arg(ap,type)  ( *(type *)((ap += _INTSIZEOF(type)) - _INTSIZEOF(type)) )
#define user_va_end(ap)       ( ap = (user_va_list)0 )

int user_vsprintf(char *buf, const char *fmt, user_va_list args);
int user_printf(const char *fmt, ...);
int user_sprintf(char *buf, const char *fmt, ...);
#endif
