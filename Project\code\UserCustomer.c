#include <stdio.h>
#include <string.h>

#include "gap_api.h"
#include "gatt_api.h"

#include "button.h"
#include "jump_table.h"
#include "os_mem.h"
#include "os_timer.h"
#include "sys_utils.h"

#include "UserBLE.h"
#include "driver_efuse.h"
#include "driver_flash.h"
#include "driver_i2s.h"
#include "driver_plf.h"
#include "driver_pmu.h"
#include "driver_rtc.h"
#include "driver_system.h"
#include "driver_uart.h"
#include "driver_wdt.h"
#include "flash_usage_config.h"

#include "UserApp.h"
#include "UserAppConfig.h"
#include "UserCustomer.h"
#include "UserKeyAdv.h"
#include "UserKeyMatrix.h"
#include "UserPLA.h"
#include "UserPrintf.h"
#include "UserUart.h"

#define Display_Debug(_x)                                                      \
  do {                                                                         \
    co_printf _x;                                                              \
  } while (0) // 启用调试输出

#define USER_CUSTOMER_RPARA_EXIST 0xAAAA5555

#define __PACKED_STRUCT_BEGIN__
#define __PACKED_STRUCT_END__ __attribute__((packed))

typedef __PACKED_STRUCT_BEGIN__ struct _tagUserCustomerPara_t {
  unsigned long SlaveMAC_Exist;
  unsigned char SlaveMAC[8];

  unsigned long RemoteID_Exist;
  unsigned char RemoteID0[4];
  unsigned char RemoteID1[4];

  unsigned long StudyRemoteID0_Exist;
  unsigned char StudyRemoteID0[4];

  unsigned long StudyRemoteID1_Exist;
  unsigned char StudyRemoteID1[4];

} __PACKED_STRUCT_END__ UserCustomerPara_t;

static UserCustomerPara_t UserCustomerPara;

int User_Customer_Load_config(void) {
  unsigned char MacAddress[6];
  unsigned char WithSave;

  WithSave = 0;

  flash_read(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
             (unsigned char *)&UserCustomerPara);

  if (User_Customer_Get_SlaveMac(MacAddress) != 0) {
    WithSave = 1;
  }

  if (WithSave) {
    // User_Customer_Save_Default_Para();
  }

  return 0;
}

int User_Customer_Set_SlaveMac(unsigned char *Mac, unsigned char WithSave) {
  if (memcmp(Mac, UserCustomerPara.SlaveMAC, 6) == 0) {
    return 0;
  }

  UserCustomerPara.SlaveMAC_Exist = USER_CUSTOMER_RPARA_EXIST;
  memcpy(UserCustomerPara.SlaveMAC, Mac, 6);

  Display_Debug(("Set SlaveMac\r\n"));

  if (WithSave) {
    flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
    flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
                (unsigned char *)&UserCustomerPara);
    Display_Debug(("SlaveMac saved\r\n"));
  }

  return 0;
}

int User_Customer_Get_SlaveMac(unsigned char *Mac) {
  if (UserCustomerPara.SlaveMAC_Exist != USER_CUSTOMER_RPARA_EXIST) {
    Display_Debug(("SlaveMac not exist\r\n"));
    return (-1);
  } else {
    memcpy(Mac, UserCustomerPara.SlaveMAC, 6);
    return 0;
  }
}

int User_Customer_Reset_SlaveMac(void) {
  Display_Debug(("Reset SlaveMac\r\n"));

  UserCustomerPara.SlaveMAC_Exist = 0xFFFFFFFF;
  memcpy(UserCustomerPara.SlaveMAC, "\xFF\xFF\xFF\xFF\xFF\xFF", 6);

  flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
  flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
              (unsigned char *)&UserCustomerPara);

  return 0;
}

int User_Customer_Set_RemoteID(unsigned char *RemoteID0,
                               unsigned char *RemoteID1,
                               unsigned char WithSave) {
  if ((memcmp(RemoteID0, UserCustomerPara.RemoteID0, 4) == 0) &&
      (memcmp(RemoteID1, UserCustomerPara.RemoteID1, 4) == 0)) {
    return 0;
  }

  UserCustomerPara.RemoteID_Exist = USER_CUSTOMER_RPARA_EXIST;
  memcpy(UserCustomerPara.RemoteID0, RemoteID0, 4);
  memcpy(UserCustomerPara.RemoteID1, RemoteID1, 4);

  Display_Debug(("Set RemoteID\r\n"));

  if (WithSave) {
    flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
    flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
                (unsigned char *)&UserCustomerPara);
    Display_Debug(("RemoteID saved\r\n"));
  }

  return 0;
}

int User_Customer_Get_RemoteID(unsigned char *RemoteID0,
                               unsigned char *RemoteID1) {
  if (UserCustomerPara.RemoteID_Exist != USER_CUSTOMER_RPARA_EXIST) {
    Display_Debug(("RemoteID not exist\r\n"));
    return (-1);
  } else {
    Display_Debug(("Get RemoteID\r\n"));
    memcpy(RemoteID0, UserCustomerPara.RemoteID0, 4);
    memcpy(RemoteID1, UserCustomerPara.RemoteID1, 4);
    return 0;
  }
}

int User_Customer_Set_StudyRemoteID0(unsigned char *StudyRemoteID0,
                                     unsigned char WithSave) {
  if (memcmp(StudyRemoteID0, UserCustomerPara.StudyRemoteID0, 4) == 0) {
    return 0;
  }

  UserCustomerPara.StudyRemoteID0_Exist = USER_CUSTOMER_RPARA_EXIST;
  memcpy(UserCustomerPara.StudyRemoteID0, StudyRemoteID0, 4);

  Display_Debug(("Set StudyRemoteID0\r\n"));

  if (WithSave) {
    flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
    flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
                (unsigned char *)&UserCustomerPara);
    Display_Debug(("StudyRemoteID0 saved\r\n"));
  }

  return 0;
}

int User_Customer_Get_StudyRemoteID0(unsigned char *StudyRemoteID0) {
  if (UserCustomerPara.StudyRemoteID0_Exist != USER_CUSTOMER_RPARA_EXIST) {
    Display_Debug(("StudyRemoteID0 not exist\r\n"));
    return (-1);
  } else {
    Display_Debug(("Get StudyRemoteID0\r\n"));
    memcpy(StudyRemoteID0, UserCustomerPara.StudyRemoteID0, 4);
    return 0;
  }
}

int User_Customer_Reset_StudyRemoteID0(void) {
  Display_Debug(("Reset StudyRemoteID0\r\n"));

  UserCustomerPara.StudyRemoteID0_Exist = 0xFFFFFFFF;
  memcpy(UserCustomerPara.StudyRemoteID0, "\xFF\xFF\xFF\xFF", 4);

  flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
  flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
              (unsigned char *)&UserCustomerPara);

  return 0;
}

int User_Customer_Set_StudyRemoteID1(unsigned char *StudyRemoteID1,
                                     unsigned char WithSave) {
  if (memcmp(StudyRemoteID1, UserCustomerPara.StudyRemoteID1, 4) == 0) {
    return 0;
  }

  UserCustomerPara.StudyRemoteID1_Exist = USER_CUSTOMER_RPARA_EXIST;
  memcpy(UserCustomerPara.StudyRemoteID1, StudyRemoteID1, 4);

  Display_Debug(("Set StudyRemoteID1\r\n"));

  if (WithSave) {
    flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
    flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
                (unsigned char *)&UserCustomerPara);
    Display_Debug(("StudyRemoteID1 saved\r\n"));
  }

  return 0;
}

int User_Customer_Get_StudyRemoteID1(unsigned char *StudyRemoteID1) {
  if (UserCustomerPara.StudyRemoteID1_Exist != USER_CUSTOMER_RPARA_EXIST) {
    Display_Debug(("StudyRemoteID1 not exist\r\n"));
    return (-1);
  } else {
    Display_Debug(("Get StudyRemoteID1\r\n"));
    memcpy(StudyRemoteID1, UserCustomerPara.StudyRemoteID1, 4);
    return 0;
  }
}

int User_Customer_Reset_StudyRemoteID1(void) {
  Display_Debug(("Reset StudyRemoteID1\r\n"));

  UserCustomerPara.StudyRemoteID1_Exist = 0xFFFFFFFF;
  memcpy(UserCustomerPara.StudyRemoteID1, "\xFF\xFF\xFF\xFF", 4);

  flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
  flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
              (unsigned char *)&UserCustomerPara);

  return 0;
}

int User_Customer_Save_Default_Para(void) {
  flash_erase(USER_CUSTOMER_PARAMETER_ADDRESS, 0);
  flash_write(USER_CUSTOMER_PARAMETER_ADDRESS, sizeof(UserCustomerPara_t),
              (unsigned char *)&UserCustomerPara);

  Display_Debug(("Save default para\r\n"));

  return 0;
}

// 简化的UART定时器（可选）
static void User_Customer_Uart_Timer_CB(void *arg) {
  unsigned short TempReadLen;
  static unsigned char User_Rcv_Buff[512];
  static unsigned short User_Rcv_Len;

  User_Rcv_Len = HAL_ConsoleRead(20, User_Rcv_Buff);

  if (User_Rcv_Len > 0) {
    HAL_ConsoleWrite(User_Rcv_Len, User_Rcv_Buff);
  }

  return;
}

static os_timer_t User_Throughput_Timer;

int User_Customer_Uart_Timer_Init(void) {
  os_timer_init(&User_Throughput_Timer, User_Customer_Uart_Timer_CB, NULL);
  os_timer_start(&User_Throughput_Timer, 10, true);
  return 0;
}

int User_Customer_Uart_Timer_Close(void) {
  os_timer_stop(&User_Throughput_Timer);
  return 0;
}

/**
 * @brief 用户初始化（简化版，保留唤醒检测）
 */
int User_CustomerInit(void) {
  uint32_t a_value;
  a_value = ool_read32(PMU_REG_RTC_ALMA_VALUE_0);

  User_Customer_Load_config();
  User_Uart_Init(115200);

  Display_Debug(("User Customer Init\r\n"));

  // 检查是否从深度睡眠唤醒（保留核心唤醒检测逻辑）
  if (a_value == 0x4C) {
    Display_Debug(("Wakeup from deep sleep detected\r\n"));
    // 处理唤醒按键
    int wakeup_result = user_key_wakeup_handler();
    if (wakeup_result == 0) {
      Display_Debug(("Wakeup key handler success\r\n"));
    } else {
      Display_Debug(("Wakeup key handler failed: %d\r\n", wakeup_result));
    }
    // 清除唤醒标记
    ool_write32(PMU_REG_RTC_ALMA_VALUE_0, 0x00);
    // 正常初始化
    User_Adv_Key_Process_Init();
  } else {
    Display_Debug(("Normal startup\r\n"));
    // 正常启动
    User_Adv_Key_Process_Init();
  }

  User_Start_Scanning(1);

  return 0;
}
