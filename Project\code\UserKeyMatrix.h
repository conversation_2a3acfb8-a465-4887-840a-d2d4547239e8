#ifndef _USER_KEY_MATRIX_H
#define _USER_KEY_MATRIX_H

#include "driver_gpio.h"
#include "driver_iomux.h"
#include "driver_system.h"
#include <stdint.h>

// 按键扫描模式（仅支持矩阵模式）
typedef enum {
  KEY_SCAN_MODE_MATRIX = 0 // 矩阵扫描模式
} key_scan_mode_t;

// 简化的按键事件类型
typedef enum {
  KEY_EVENT_PRESS = 0 // 单一事件类型，直接发送当前键值
} key_event_t;

// 按键信息结构体（简化版）
typedef struct {
  uint32_t key_mask; // 按键掩码（包括0值）
  key_event_t event; // 按键事件（固定为PRESS）
  uint16_t duration; // 保留字段，固定为0
} key_info_t;

// 按键回调函数类型
typedef void (*key_callback_t)(key_info_t *key_info);

// 按键配置结构体（简化版）
typedef struct {
  uint16_t scan_interval;  // 扫描间隔(ms)
  key_callback_t callback; // 事件回调函数
} key_config_t;

// 矩阵按键配置
#define KEY_MATRIX_ROWS 4
#define KEY_MATRIX_COLS 5
#define KEY_MATRIX_MAX_KEYS 20

// 引脚配置结构体
typedef struct {
  enum system_port_t port;    // GPIO端口
  enum system_port_bit_t bit; // GPIO位
  uint32_t func;              // 引脚功能枚举
  uint32_t pull_pin;          // 上拉引脚定义枚举
} gpio_pin_config_t;

// 矩阵按键引脚配置结构体
typedef struct {
  gpio_pin_config_t rows[KEY_MATRIX_ROWS]; // 行引脚配置
  gpio_pin_config_t cols[KEY_MATRIX_COLS]; // 列引脚配置
} matrix_key_pin_config_t;

// ==================== 核心接口函数 ====================

/**
 * @brief 按键驱动初始化
 * @param config 配置参数
 * @return 0-成功，负数-错误码
 */
int user_key_init(key_config_t *config);

/**
 * @brief 启动按键扫描（BLE连接成功后调用）
 * @return 0-成功，负数-错误码
 */
int user_key_start_scanning(void);

/**
 * @brief 停止按键扫描
 * @return 0-成功
 */
int user_key_stop_scanning(void);

/**
 * @brief 手动触发按键扫描
 * @return 0-成功
 */
int user_key_scan(void);

/**
 * @brief 进入低功耗模式
 * @return 0-成功，-2-有按键按下拒绝进入
 */
int user_key_enter_low_power(void);

// ==================== 唤醒处理接口 ====================

/**
 * @brief 唤醒处理函数（保留快速检测逻辑）
 * @return 0-成功，负数-错误码
 */
int user_key_wakeup_handler(void);

/**
 * @brief 检查并处理缓存的唤醒按键
 * @return 0-无缓存按键，1-有缓存按键已处理
 */
int user_key_check_wakeup_cache(void);

/**
 * @brief 检查是否有唤醒按键等待发送
 * @return 0-无等待按键，1-有按键等待发送
 */
int user_key_has_pending_wakeup(void);

/**
 * @brief 获取BLE连接状态
 * @return 0-未连接，1-已连接可发送
 */
int user_key_is_ble_connecting(void);

/**
 * @brief 获取当前硬件按键状态（实时扫描）
 * @return 当前按键掩码，0表示无按键按下
 */
uint32_t user_key_get_current_hardware_state(void);

// ==================== 低功耗配置接口 ====================

/**
 * @brief 矩阵按键低功耗配置
 * @return 0-成功
 */
int user_key_matrix_low_power_config(void);

#endif /* _USER_KEY_MATRIX_H */
