/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:48
 * @LastEditTime: 2023-06-07 17:03:10
 * @Description:  This file is used to define user uart function.
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */
#ifndef _USER_UART_H
#define _USER_UART_H

int User_Uart_Init(unsigned long baud_rate);
int HAL_ConsoleWrite(int Length, unsigned char *Buffer);
int HAL_ConsoleRead(int Length, unsigned char *Buffer);
void HAL_UART_Int_Init(unsigned long baud_rate);
unsigned short HAL_UART_Int_Send(unsigned char *Data, unsigned short Length);
unsigned short HAL_UART_Int_Recv(unsigned char *Data, unsigned short Length);
unsigned short HAL_UART_Int_GetRxCount(void);
unsigned short HAL_UART_Int_GetRxFree(void);
void HAL_UART_Int_ClearRxFifo(void);
#endif
