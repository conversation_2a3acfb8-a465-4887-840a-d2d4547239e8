/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:48
 * @LastEditTime: 2023-06-07 17:12:01
 * @Description:
 *
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved.
 */
#ifndef BLE_MULTI_ROLE_H
#define BLE_MULTI_ROLE_H
 

enum BLE_ROLE
{
    BLE_ROLE_PERIPHERAL=0,
    BLE_ROLE_CENTRAL = 1,
};

/**
 * @brief For sending data to the slave device
 * 
 * @param con_idx connect index
 * @param data data
 * @param len length
 * @param att_idx Attribute id number
 * @return 0.
 */
int user_spple_writenors_data(uint8_t con_idx, uint8_t *data, uint16_t len, uint8_t att_idx);

/**
 * @brief Application layer GAP event callback function. Handles GAP evnets.
 * 
 * @param p_event GAP events from BLE stack.
 * @return None.
 */
void app_gap_evt_cb(gap_event_t *p_event);

/**
 * @brief Initialize multi role, including peripheral & central roles, 
 *        and BLE related parameters.
 * 
 * @param None. 
 * @return None.
 */
void multi_role_init(void);
#endif
