/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:48
 * @LastEditTime: 2023-06-07 16:37:34
 * @Description:  This file is used to define user PLA function.
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */
#ifndef _USER_PLA_H
#define _USER_PLA_H

int User_PLA_GPIO_Init(void);
int User_PLA_Set_Connected_Pin_High(void);
int User_PLA_Set_Connected_Pin_Low(void);
int User_PLA_Power_Off(void);
int User_PLA_Sleep_Init(void);
int User_PLA_Exit_Sleep_From_GPIO(void);
int User_PLA_Exit_Sleep_From_BLE_Conn(void);
int User_PLA_Enter_Sleep_From_BLE_Conn(void);
unsigned char User_PLA_Cmd_Enter_Sleep(void);
#endif
