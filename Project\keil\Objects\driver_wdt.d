.\objects\driver_wdt.o: ..\..\components\driver\driver_wdt.c
.\objects\driver_wdt.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\driver_wdt.o: ..\..\components\modules\common\include\co_log.h
.\objects\driver_wdt.o: ..\..\components\modules\common\include\co_printf.h
.\objects\driver_wdt.o: ..\..\components\modules\sys\include\sys_utils.h
.\objects\driver_wdt.o: ..\..\components\modules\platform\include\compiler.h
.\objects\driver_wdt.o: ..\..\components\driver\include\driver_wdt.h
.\objects\driver_wdt.o: ..\..\components\modules\common\include\co_math.h
.\objects\driver_wdt.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\driver_wdt.o: C:\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\driver_wdt.o: ..\..\components\driver\include\driver_pmu.h
.\objects\driver_wdt.o: ..\..\components\driver\include\driver_iomux.h
.\objects\driver_wdt.o: ..\..\components\driver\include\driver_pmu_regs.h
.\objects\driver_wdt.o: ..\..\components\driver\include\driver_frspim.h
