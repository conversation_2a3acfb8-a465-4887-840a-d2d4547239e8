/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:48
 * @LastEditTime: 2023-06-07 14:48:42
 * @Description: 
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */
#ifndef _USER_ATCMD_H
#define _USER_ATCMD_H

void BleCmdData_Init(void);
unsigned short Ble_Cmd_Input_Data_Write(unsigned char *Data, unsigned short Length);
unsigned short Ble_Cmd_Input_Data_Read(unsigned char *Data, unsigned short Length);
unsigned short Ble_Cmd_Output_Data_Write(unsigned char *Data, unsigned short Length);
unsigned short Ble_Cmd_Output_Data_Read(unsigned char *Data, unsigned short Length);

int user_printf_Cmd_Ack(const char *fmt, ...);
void Set_Cmd_Output_Use_Uart(void);
void Set_Cmd_Output_Use_BLE(void);

void AddATCommands(void);

void start_feed_dog_timer(void);
void stop_feed_dog_timer(void);

void ProcessCommandLine(void *arg);
#endif
