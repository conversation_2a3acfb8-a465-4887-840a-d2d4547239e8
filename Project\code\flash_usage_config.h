/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:48
 * @LastEditTime: 2023-06-07 16:45:34
 * @Description: This file is used to define flash space used in SDK.
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */


#ifndef _FLASH_USAGE_CONFIG_H
#define _FLASH_USAGE_CONFIG_H


/*
 * the following MACROS is used to define flash space used in SDK.
 * user can change these values according their applications.
 */

#define FOR_4M_FLASH // such as FR8016HA FR8012HA

#ifdef FOR_4M_FLASH
#define FLASH_PAGE_SIZE 0x1000
#define IMAGE_SIZE 0x28000 // imageA_size:160K(0x28000)    imageB_size:160K(0x50000)

#define FLASH_MAX_SIZE 0x80000
#define JUMP_TABLE_STATIC_KEY_OFFSET 0x7F000
#define BLE_REMOTE_SERVICE_SAVE_ADDR 0x7E000
#define BLE_BONDING_INFO_SAVE_ADDR 0x7D000
#define USER_PARAMETER_SIZE			1024*8
#define USER_PARAMETER_ADDRESS 0x7B000 // WLT for  user parameter
#define USER_MAC_PAGE_ADDR 0x7A000 // WLT for  mac addr
#define USER_MAC_BACKUP_PAGE_ADDR 0x79000

#endif // FOR_4M_FLASH

/*
 * uncomment this MACRO if user need protect flash from unexpected erase or write operation
 */
//#define FLASH_PROTECT

#endif
