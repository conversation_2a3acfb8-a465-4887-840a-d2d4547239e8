/**
 * Copyright (c) 2019, Freqchip
 * 
 * All rights reserved.
 * 
 * 
 */
 
/*
 * INCLUDES (����ͷ�ļ�)
 */
#include <stdio.h>
#include <string.h>
#include "co_printf.h"
#include "gap_api.h"
#include "gatt_api.h"
#include "gatt_sig_uuid.h"

#include "User_Spple_Service.h"

#include "UserApp.h"
#include "UserUart.h"
#include "UserPrintf.h"

#define Display_Debug(_x)                                //do { user_printf _x; } while(0)

/*
 * MACROS (�궨��)
 */

/*
 * CONSTANTS (��������)
 */


// Spple Service UUID: 0xFFF0
uint8_t spple_svc_uuid[] = SPPLE_SVC_UUID;

/******************************* Spple Dataout defination *******************************/

#define SPPLE_DATAOUT_VALUE_LEN  256

#define SPPLE_DATAOUT_CCC_LEN   2
uint8_t spple_dataout_ccc[SPPLE_DATAOUT_CCC_LEN] = {0};

#define SPPLE_DATAOUT_DESC_LEN   13
const uint8_t spple_dataout_desc[SPPLE_DATAOUT_DESC_LEN] = "Module->Phone";

/******************************* Spple Datain defination ********************************/

#define SPPLE_DATAIN_VALUE_LEN  256

#define SPPLE_DATAIN_DESC_LEN   13
const uint8_t spple_datain_desc[SPPLE_DATAIN_DESC_LEN] = "Phone->Module";

/*
 * TYPEDEFS (���Ͷ���)
 */

/*
 * GLOBAL VARIABLES (ȫ�ֱ���)
 */
uint8_t spple_svc_id = 0;

/*
 * LOCAL VARIABLES (���ر���)
 */
static gatt_service_t spple_service_svc;

/*********************************************************************
 * Profile Attributes - Table
 * ÿһ���һ��attribute�Ķ��塣
 * ��һ��attributeΪService �ĵĶ��塣
 * ÿһ������ֵ(characteristic)�Ķ��壬�����ٰ�������attribute�Ķ��壻
 * 1. ����ֵ����(Characteristic Declaration)
 * 2. ����ֵ��ֵ(Characteristic value)
 * 3. ����ֵ������(Characteristic description)
 * �����notification ����indication �Ĺ��ܣ��������ĸ�attribute�Ķ��壬����ǰ�涨���������������һ������ֵ�ͻ�������(client characteristic configuration)��
 *
 */
gatt_attribute_t spple_service_att_table[SPPLE_IDX_NB] =
{
        // Spple Service Declaration
        [SPPLE_IDX_SERVICE]                 =   {
                                                    { UUID_SIZE_2, UUID16_ARR(GATT_PRIMARY_SERVICE_UUID) },     /* UUID */
                                                    GATT_PROP_READ,                                             /* Permissions */
                                                    UUID_SIZE_2,                                                /* Max size of the value */     /* Service UUID size in service declaration */
                                                    (uint8_t*)&spple_svc_uuid,                                   /* Value of the attribute */    /* Service UUID value in service declaration */
                                                },

        // Data Out Declaration           
        [SPPLE_IDX_DATAOUT_DECLARATION]     =   {
                                                    { UUID_SIZE_2, UUID16_ARR(GATT_CHARACTER_UUID) },           /* UUID */
                                                    GATT_PROP_READ,                                             /* Permissions */
                                                    0,                                                          /* Max size of the value */
                                                    NULL,                                                       /* Value of the attribute */
                                                },
        // Data Out Value                  
        [SPPLE_IDX_DATAOUT_VALUE]           =   {
                                                    { UUID_SIZE_2, SPPLE_DATAOUT_UUID },                       /* UUID */
                                                    GATT_PROP_NOTI,                                             /* Permissions */
                                                    SPPLE_DATAOUT_VALUE_LEN,                                    /* Max size of the value */
                                                    NULL,                                                       /* Value of the attribute */    /* Can assign a buffer here, or can be assigned in the application by user */
                                                },        

        // Data Out characteristic configuration
        [SPPLE_IDX_DATAOUT_CFG]             =   {
                                                    { UUID_SIZE_2, UUID16_ARR(GATT_CLIENT_CHAR_CFG_UUID) },     /* UUID */
                                                    GATT_PROP_READ | GATT_PROP_WRITE,                           /* Permissions */
                                                    2,                                                          /* Max size of the value */
                                                    NULL,                                                       /* Value of the attribute */    /* Can assign a buffer here, or can be assigned in the application by user */
                                                }, 
																								
        // Data Out User Description
        [SPPLE_IDX_DATAOUT_USER_DESCRIPTION]=   {
                                                    { UUID_SIZE_2, UUID16_ARR(GATT_CHAR_USER_DESC_UUID) },      /* UUID */
                                                    GATT_PROP_READ,                                             /* Permissions */
                                                    SPPLE_DATAOUT_DESC_LEN,                                     /* Max size of the value */
                                                    (uint8_t *)spple_dataout_desc,                              /* Value of the attribute */
                                                },


        // Data In Declaration
        [SPPLE_IDX_DATAIN_DECLARATION]      =   {
                                                    { UUID_SIZE_2, UUID16_ARR(GATT_CHARACTER_UUID) },           /* UUID */
                                                    GATT_PROP_READ,                                             /* Permissions */
                                                    0,                                                          /* Max size of the value */
                                                    NULL,                                                       /* Value of the attribute */
                                                },
        // Data In Value   
        [SPPLE_IDX_DATAIN_VALUE]            =   {
                                                    { UUID_SIZE_2, SPPLE_DATAIN_UUID },                         /* UUID */
                                                    GATT_PROP_WRITE_CMD,                                         /* Permissions */
                                                    SPPLE_DATAIN_VALUE_LEN,                                      /* Max size of the value */
                                                    NULL,                                                        /* Value of the attribute */	/* Can assign a buffer here, or can be assigned in the application by user */
                                                },   
        // Data In User Description
        [SPPLE_IDX_DATAIN_USER_DESCRIPTION] =   {
                                                    { UUID_SIZE_2, UUID16_ARR(GATT_CHAR_USER_DESC_UUID) },       /* UUID */
                                                    GATT_PROP_READ,                                              /* Permissions */
                                                    SPPLE_DATAIN_DESC_LEN,                                       /* Max size of the value */
                                                    (uint8_t *)spple_datain_desc,                                /* Value of the attribute */
                                                },
};

int user_spple_notify_data(uint8_t con_idx, uint8_t *data, uint16_t len)
{
    gatt_ntf_t ntf_att;
    
    ntf_att.att_idx = SPPLE_IDX_DATAOUT_VALUE;
    ntf_att.conidx = con_idx;
    ntf_att.svc_id = spple_svc_id;
    ntf_att.data_len = len;
    ntf_att.p_data = data;
    
    gatt_notification(ntf_att);
    
    return 0;
}
/*********************************************************************
 * @fn      sp_gatt_msg_handler
 *
 * @brief   Simple Profile callback funtion for GATT messages. GATT read/write
 *			operations are handeled here.
 *
 * @param   p_msg       - GATT messages from GATT layer.
 *
 * @return  uint16_t    - Length of handled message.
 */
static uint16_t spple_gatt_msg_handler(gatt_msg_t *p_msg)
{
    
    switch(p_msg->msg_evt)
    {
        case GATTC_MSG_READ_REQ:
				{
/*********************************************************************
 * @brief   Simple Profile user application handles read request in this callback.
 *			Ӧ�ò�������ص��������洦����������
 *
 * @param   p_msg->param.msg.p_msg_data  - the pointer to read buffer. NOTE: It's just a pointer from lower layer, please create the buffer in application layer.
 *					  ָ�����������ָ�롣 ��ע����ֻ��һ��ָ�룬����Ӧ�ó����з��仺����. Ϊ�������, ���Ϊָ���ָ��.
 *          p_msg->param.msg.msg_len     - the pointer to the length of read buffer. Application to assign it.
 *                    ���������ĳ��ȣ��û�Ӧ�ó���ȥ������ֵ.
 *          p_msg->att_idx - index of the attribute value in it's attribute table.
 *					  Attribute��ƫ����.
 *
 * @return  ������ĳ���.
 */					
					if(p_msg->att_idx == SPPLE_IDX_DATAOUT_CFG)
					{
						memcpy(p_msg->param.msg.p_msg_data, spple_dataout_ccc, 2);
						return 2;
					}
				}
            break;
        
        case GATTC_MSG_WRITE_REQ:
				{
/*********************************************************************
 * @brief   Simple Profile user application handles write request in this callback.
 *			Ӧ�ò�������ص��������洦��д������
 *
 * @param   p_msg->param.msg.p_msg_data   - the buffer for write
 *			              д����������.
 *					  
 *          p_msg->param.msg.msg_len      - the length of write buffer.
 *                        д�������ĳ���.
 *          att_idx     - index of the attribute value in it's attribute table.
 *					      Attribute��ƫ����.
 *
 * @return  д����ĳ���.
 */				
					if (p_msg->att_idx == SPPLE_IDX_DATAIN_VALUE)
					{
                        if(User_Get_BT_RunMode() != BT_RUNMODE_TRANSPARENT_MODE )//非单从机模式，会打印出connection index，数据长度，数据内容
                        {
                            user_printf("+IND=RECVS,%u,%u,", User_Get_ConnIndex_By_Libconidx(p_msg->conn_idx), p_msg->param.msg.msg_len);
                            HAL_ConsoleWrite(p_msg->param.msg.msg_len, p_msg->param.msg.p_msg_data);
                            user_printf("\r\n");
                        }
                        else//单从机模式，只打印数据内容
                        {
                            HAL_ConsoleWrite(p_msg->param.msg.msg_len, p_msg->param.msg.p_msg_data);
                        }
					}
                    else if (p_msg->att_idx == SPPLE_IDX_DATAOUT_CFG)
					{
						Display_Debug(("SPPLE_Recv_CCC\r\n"));
						if(p_msg->param.msg.p_msg_data[0] & 0x01)
                        {
                            User_Set_CanSend_Flag(p_msg->conn_idx, 1);
                        }
                        else
                        {
                            User_Set_CanSend_Flag(p_msg->conn_idx, 0);
                        }
					}
				}
            break;
        case GATTC_MSG_LINK_CREATE:
            Display_Debug(("link_created\r\n"));
            break;
        case GATTC_MSG_LINK_LOST:
            Display_Debug(("link_lost\r\n"));
            break;  
        case GATTC_MSG_CMP_EVT:
            if(p_msg->param.op.operation == GATT_OP_NOTIFY)
            {
                
                if(p_msg->param.op.status != 0)
                {
//                    co_printf("notify_cmp=%d\r\n",p_msg->param.op.status);
                }
                // if(p_msg->param.op.status == BLE_SUCCESS)
                // {
                //     User_Set_CanSend_Flag(p_msg->conn_idx, 1);
                // }
                // else
                // {
                //     User_Set_CanSend_Flag(p_msg->conn_idx, 0);
                // }
            }
            // else if(p_msg->param.op.operation == GATTC_INDICATE)
            // {
            //     Display_Debug(("indicate_cmp\r\n"));
            // }

            break;  
        default:
            break;
    }
    return p_msg->param.msg.msg_len;
}

int user_spple_gatt_add_service(void)
{
	uint16_t peer_deviece_service_uuid=0;
  uint16_t peer_deviece_notify_uuid=0;
  uint16_t peer_deviece_write_uuid=0;
	
	User_Get_Default_ServiceUUID(&peer_deviece_service_uuid, &peer_deviece_notify_uuid, &peer_deviece_write_uuid);
	memcpy(spple_svc_uuid, &peer_deviece_service_uuid, 2);
	memcpy(spple_service_att_table[2].uuid.p_uuid, &peer_deviece_notify_uuid, 2);
	memcpy(spple_service_att_table[6].uuid.p_uuid, &peer_deviece_write_uuid, 2);
	
	spple_service_svc.p_att_tb = spple_service_att_table;
	spple_service_svc.att_nb = SPPLE_IDX_NB;
	spple_service_svc.gatt_msg_handler = spple_gatt_msg_handler;
	
	spple_svc_id = gatt_add_service(&spple_service_svc);
	  
    return 0;
}





