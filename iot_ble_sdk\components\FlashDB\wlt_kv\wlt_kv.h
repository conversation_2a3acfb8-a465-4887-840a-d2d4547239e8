/*
 * @Author: eric
 * @Date: 2023-06-06 15:40:50
 * @LastEditTime: 2023-06-07 14:59:26
 * @Description: flash kv database interface
 * 
 * Copyright (c) 2023, Wi-linktech Communication Technology (Shanghai) Co., Ltd, All Rights Reserved. 
 */



#ifndef _WLT_KV_H_
#define _WLT_KV_H_

/**
 * @brief Initialize the key-value database.
 * 
 * @param partname The name of the partition to store the database.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_init(const char *partname);

/**
 * @brief Set a key-value pair in the database.
 * 
 * @param key The key to set.
 * @param value The value to set.
 * @param len The length of the value.
 * @param sync Whether to sync the database after setting the value.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_set(const char *key, const void *value, int len, int flag);

/**
 * @brief Get a value from the database.
 * 
 * @param key The key to get.
 * @param buffer The buffer to store the value.
 * @param buffer_len The length of the buffer.
 * @return int The length of the value on success, negative error on failure.
 */
int wlt_kv_get(const char *key, void *buffer, int *buffer_len);

/**
 * @brief Set a key-value pair in the database with a float value.
 * 
 * @param key The key to set.
 * @param v The float value to set.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_setfloat(const char *key, float v);

/**
 * @brief Get a float value from the database.
 * 
 * @param key The key to get.
 * @param value The float value to store the value.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_getfloat(const char *key, float *v);

/**
 * @brief Set a key-value pair in the database with an integer value.
 * 
 * @param key The key to set.
 * @param v The integer value to set.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_setint(const char *key, int v);

/**
 * @brief Get an integer value from the database.
 * 
 * @param key The key to get.
 * @param value The integer value to store the value.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_getint(const char *key, int *v);

/**
 * @brief Set a key-value pair in the database with a string value.
 * 
 * @param key The key to set.
 * @param v The string value to set.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_setstring(const char *key, const char *v);

int wlt_kv_getstring(const char *key, char *v, int len);

/**
 * @brief Delete a key-value pair from the database.
 * 
 * @param key The key to delete.
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_del(const char *key);

/**
 * @brief Reset the key-value database to default values.
 * 
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_reset(void);

/**
 * @brief Print all key-value pairs in the database.
 * 
 * @return int 0 on success, negative error on failure.
 */
int wlt_kv_print(void);

#endif
